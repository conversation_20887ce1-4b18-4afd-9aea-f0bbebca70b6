#!/usr/bin/env python3
"""
Script to create test reservation data for testing the /order/customer/send endpoint.
Creates a reservation with status=8 (ready for insert) that can be used for testing.
"""

import mysql.connector
from uuid import uuid4
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_db_connection():
    """Get database connection"""
    return mysql.connector.connect(
        host=os.getenv("DB_HOST", "localhost"),
        user=os.getenv("DB_USER", "root"),
        password=os.getenv("DB_PASSWORD", ""),
        database=os.getenv("DB_NAME", "smartbox")
    )

def create_test_reservation():
    """Create a test reservation with status=8 for testing"""
    print("🗄️  Creating test reservation...")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Create test reservation
        reservation_uuid = str(uuid4())
        box_uuid = os.getenv("BOX_UUID", "default-box")
        test_pin = "123456"
        
        query = """
            INSERT INTO order_reservations 
            (uuid, box_uuid, section_id, status, insert_pin, phone_number, expired, type, created_at, last_update)
            VALUES (%s, %s, %s, 8, %s, %s, 0, 'customer_send', NOW(), NOW())
        """
        
        cursor.execute(query, (
            reservation_uuid, 
            box_uuid, 
            None,  # section_id will be set when customer selects section
            test_pin,
            "123456789"  # test phone number
        ))
        
        conn.commit()
        reservation_id = cursor.lastrowid
        
        print(f"✅ Test reservation created:")
        print(f"   - ID: {reservation_id}")
        print(f"   - UUID: {reservation_uuid}")
        print(f"   - PIN: {test_pin}")
        print(f"   - Status: 8 (ready for insert)")
        print(f"   - Type: customer_send")
        
        # Also create one with a pre-assigned section for testing
        reservation_uuid2 = str(uuid4())
        test_pin2 = "654321"
        
        cursor.execute(query, (
            reservation_uuid2, 
            box_uuid, 
            "3",  # pre-assigned section
            test_pin2,
            "987654321"
        ))
        
        conn.commit()
        reservation_id2 = cursor.lastrowid
        
        print(f"\n✅ Second test reservation created:")
        print(f"   - ID: {reservation_id2}")
        print(f"   - UUID: {reservation_uuid2}")
        print(f"   - PIN: {test_pin2}")
        print(f"   - Status: 8 (ready for insert)")
        print(f"   - Section: 3 (pre-assigned)")
        print(f"   - Type: customer_send")
        
        cursor.close()
        conn.close()
        
        print(f"\n🧪 You can now test the endpoint with:")
        print(f"   - PIN {test_pin} (no pre-assigned section)")
        print(f"   - PIN {test_pin2} (pre-assigned to section 3)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test reservation: {e}")
        return False

def list_existing_reservations():
    """List existing reservations for reference"""
    print("\n📋 Existing order reservations:")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("""
            SELECT id, uuid, section_id, status, insert_pin, pickup_pin, phone_number, type, created_at
            FROM order_reservations 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        reservations = cursor.fetchall()
        
        if reservations:
            print(f"{'ID':<5} {'PIN':<8} {'Section':<8} {'Status':<7} {'Type':<15} {'Created'}")
            print("-" * 70)
            for res in reservations:
                created = res['created_at'].strftime('%Y-%m-%d %H:%M')
                section = res['section_id'] or 'None'
                pin = res['insert_pin'] or res['pickup_pin'] or 'None'
                print(f"{res['id']:<5} {pin:<8} {section:<8} {res['status']:<7} {res['type'] or 'None':<15} {created}")
        else:
            print("No reservations found.")
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error listing reservations: {e}")

def main():
    """Main function"""
    print("🚀 Order Reservation Test Data Creator")
    print("=" * 50)
    
    # List existing reservations first
    list_existing_reservations()
    
    # Create test reservations
    success = create_test_reservation()
    
    if success:
        print("\n🎉 Test data created successfully!")
        print("\nNext steps:")
        print("1. Start your FastAPI server")
        print("2. Run: python test_customer_send.py")
        print("3. Test with the created PINs")
    else:
        print("\n❌ Failed to create test data")

if __name__ == "__main__":
    main()
