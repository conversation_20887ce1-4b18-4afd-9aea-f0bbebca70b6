from fastapi import APIRouter, Depends
from storage.models import StorageCategoriesResponse, StorageCategory, StorageInsertRequest, StoragePickupRequest, StorageSectionsResponse, StorageSection
from domains.storage.service import storage_service
from infrastructure.repositories.storage_repository import StorageRepository
from managers.timeline_logger import log_timeline_event

router = APIRouter()



@router.get("/categories", response_model=StorageCategoriesResponse)
async def get_storage_categories():
    """
    Returns a list of all storage categories.
    """
    repo = StorageRepository()
    all_sections = repo.get_storage_sections()
    categories_data = repo.get_all_categories()

    available_sections_by_category = {}
    for section in all_sections:
        if section['is_available']:
            cat = section['size_category']
            if cat not in available_sections_by_category:
                available_sections_by_category[cat] = 0
            available_sections_by_category[cat] += 1

    categories = []
    for category in categories_data:
        size_category = category['size_category']
        is_available = available_sections_by_category.get(size_category, 0) > 0
        categories.append(StorageCategory(**category, is_available=is_available))

    return StorageCategoriesResponse(success=True, categories=categories)

@router.get("/sections", response_model=StorageSectionsResponse)
async def get_storage_sections():
    """
    Returns a list of all storage sections with availability status.
    Only returns sections with mode = 'storage'.
    """
    repo = StorageRepository()
    sections_data = repo.get_storage_sections()
    sections = [StorageSection(**section) for section in sections_data]
    return StorageSectionsResponse(success=True, products=sections, total_count=len(sections))

@router.post("/insert")
async def insert_storage(request: StorageInsertRequest):
    """
    Initiates the storage insertion process.
    Uses the universal select_sections() function for uniform behavior when size_category is provided.
    """

    if request.section_id is not None:
        response = await storage_service.insert_storage(request.section_id, request.email)
    elif request.size_category is not None:
        # Get available sections for the category
        repo = StorageRepository()
        all_sections = repo.get_storage_sections()
        available_sections = [
            s['section_id'] for s in all_sections
            if s['size_category'] == request.size_category and s['is_available']
        ]

        if not available_sections:
            return {"success": False, "error": "No available sections for this category"}

        log_timeline_event(
            event_type="storage_process",
            event_result="section_selection_started",
            mode="storage",
            message=f"Starting section selection for category {request.size_category}"
        )

        # Create session for section selection using select_sections()
        response = await storage_service.create_selection_session(
            available_sections=available_sections,
            size_category=request.size_category,
            email=request.email
        )
    else:
        log_timeline_event(
            event_type="storage_process",
            event_result="box_selection_failed",
            mode="storage",
        )
        return {"success": False, "error": "Either section_id or size_category must be provided"}

    # If the service returns an error response, return it directly (don't raise HTTPException)
    if not response.get("success", True):
        return response

    return response

@router.post("/insert-with-selection")
async def insert_storage_with_selection(request: StorageInsertRequest):
    """
    Initiates the storage insertion process with interactive section selection.
    Uses the universal select_sections() function for uniform behavior across modules.
    """
    if request.size_category is None:
        return {"success": False, "error": "size_category must be provided for section selection"}

    # Get available sections for the category
    repo = StorageRepository()
    all_sections = repo.get_storage_sections()
    available_sections = [
        s['section_id'] for s in all_sections
        if s['size_category'] == request.size_category and s['is_available']
    ]

    if not available_sections:
        return {"success": False, "error": "No available sections for this category"}

    log_timeline_event(
        event_type="storage_process",
        event_result="section_selection_started",
        mode="storage",
        message=f"Starting section selection for category {request.size_category}"
    )

    # Create session for section selection
    response = await storage_service.create_selection_session(
        available_sections=available_sections,
        size_category=request.size_category,
        email=request.email
    )

    return response

@router.post("/pickup")
async def pickup_storage(request: StoragePickupRequest):
    """
    Initiates the storage pickup process.
    """
    response = await storage_service.pickup_storage(request.reservation_pin)

    # If the service returns an error response, return it directly (don't raise HTTPException)
    if not response.get("success", True):
        return response

    return response
