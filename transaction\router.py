from fastapi import APIRouter, HTTPException
from typing import Optional
import logging
from datetime import datetime

from .models import TransactionCallback, PaymentCallback, TransactionType
from .manager import transaction_manager
from managers import session_manager, SessionType

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/callback", response_model=TransactionCallback)
async def transaction_callback(callback: PaymentCallback) -> TransactionCallback:
    """
    Handle callback from payment terminal
    """
    logger.info(f"Received payment callback: {callback}")
    
    try:
        # Find active payment session (either TRANSACTION, PRODUCT_FLOW, PRODUCT_PICKUP, or STORAGE_FLOW)
        transaction_sessions = session_manager.get_sessions_by_type(SessionType.TRANSACTION)
        product_flow_sessions = session_manager.get_sessions_by_type(SessionType.PRODUCT_FLOW)
        product_pickup_sessions = session_manager.get_sessions_by_type(SessionType.PRODUCT_PICKUP)
        storage_flow_sessions = session_manager.get_sessions_by_type(SessionType.STORAGE_FLOW)

        logger.info(f"Looking for active payment session - Transaction sessions: {len(transaction_sessions)}, Product flow sessions: {len(product_flow_sessions)}, Product pickup sessions: {len(product_pickup_sessions)}, Storage flow sessions: {len(storage_flow_sessions)}")
        
        active_session = None
        
        # Check transaction sessions first (legacy)
        for session in transaction_sessions:
            if session.transaction_data.get("transaction_type") == TransactionType.PAYMENT:
                active_session = session
                break
        
        # If not found, check product flow sessions
        if not active_session:
            # First try to find session by variable_symbol (session_id) if available
            if callback.variable_symbol:
                for session in product_flow_sessions:
                    if session.session_id == callback.variable_symbol:
                        active_session = session
                        logger.info(f"Found product flow session by variable_symbol (session_id): {session.session_id}")
                        break

            # If not found by variable_symbol, try to find the most recent session with active WebSocket connection
            if not active_session and len(product_flow_sessions) > 0:
                from managers.ws_manager import ws_manager
                # Find all sessions with active WebSocket connections
                active_websocket_sessions = [
                    session for session in product_flow_sessions
                    if ws_manager.is_connected(session.session_id)
                ]

                if active_websocket_sessions:
                    # Sort by creation time (most recent first) and take the newest one
                    active_websocket_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = active_websocket_sessions[0]
                    logger.info(f"Found most recent active product flow session with WebSocket: {active_session.session_id}")
                    if len(active_websocket_sessions) > 1:
                        logger.warning(f"Multiple active WebSocket sessions found ({len(active_websocket_sessions)}), using most recent")

                # If no session with active WebSocket found, take the most recent one
                if not active_session:
                    # Sort by creation time (most recent first)
                    product_flow_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = product_flow_sessions[0]
                    logger.info(f"Using most recent product flow session: {active_session.session_id}")
                    logger.warning(f"No active WebSocket found for session {active_session.session_id}, this may indicate an issue")

        # If not found, check product pickup sessions
        if not active_session:
            # First try to find session by variable_symbol (session_id) if available
            if callback.variable_symbol:
                for session in product_pickup_sessions:
                    if session.session_id == callback.variable_symbol:
                        active_session = session
                        logger.info(f"Found product pickup session by variable_symbol (session_id): {session.session_id}")
                        break

            # If not found by variable_symbol, try to find the most recent session with active WebSocket connection
            if not active_session and len(product_pickup_sessions) > 0:
                from managers.ws_manager import ws_manager
                # Find all sessions with active WebSocket connections
                active_websocket_sessions = [
                    session for session in product_pickup_sessions
                    if ws_manager.is_connected(session.session_id)
                ]

                if active_websocket_sessions:
                    # Sort by creation time (most recent first) and take the newest one
                    active_websocket_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = active_websocket_sessions[0]
                    logger.info(f"Found most recent active product pickup session with WebSocket: {active_session.session_id}")
                    if len(active_websocket_sessions) > 1:
                        logger.warning(f"Multiple active WebSocket sessions found ({len(active_websocket_sessions)}), using most recent")

                # If no session with active WebSocket found, take the most recent one
                if not active_session:
                    # Sort by creation time (most recent first)
                    product_pickup_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = product_pickup_sessions[0]
                    logger.info(f"Using most recent product pickup session: {active_session.session_id}")
                    logger.warning(f"No active WebSocket found for session {active_session.session_id}, this may indicate an issue")

        # If not found, check storage flow sessions
        if not active_session:
            logger.info(f"Transaction router: Checking storage flow sessions. Variable symbol: {callback.variable_symbol}")
            logger.info(f"Transaction router: Available storage flow sessions: {[s.session_id for s in storage_flow_sessions]}")

            # First try to find session by variable_symbol (session_id) if available
            if callback.variable_symbol:
                for session in storage_flow_sessions:
                    logger.info(f"Transaction router: Comparing {session.session_id} with {callback.variable_symbol}")
                    if session.session_id == callback.variable_symbol:
                        active_session = session
                        logger.info(f"Transaction router: Found storage flow session by variable_symbol (session_id): {session.session_id}")
                        break

            # If not found by variable_symbol, try to find the most recent session with active WebSocket connection
            if not active_session and len(storage_flow_sessions) > 0:
                from managers.ws_manager import ws_manager
                # Find all sessions with active WebSocket connections
                active_websocket_sessions = [
                    session for session in storage_flow_sessions
                    if ws_manager.is_connected(session.session_id)
                ]

                if active_websocket_sessions:
                    # Sort by creation time (most recent first) and take the newest one
                    active_websocket_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = active_websocket_sessions[0]
                    logger.info(f"Found most recent active storage flow session with WebSocket: {active_session.session_id}")
                    if len(active_websocket_sessions) > 1:
                        logger.warning(f"Multiple active WebSocket sessions found ({len(active_websocket_sessions)}), using most recent")

                # If no session with active WebSocket found, take the most recent one
                if not active_session:
                    # Sort by creation time (most recent first)
                    storage_flow_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = storage_flow_sessions[0]
                    logger.info(f"Using most recent storage flow session: {active_session.session_id}")
                    logger.warning(f"No active WebSocket found for session {active_session.session_id}, this may indicate an issue")

        if not active_session:
            raise HTTPException(
                status_code=404,
                detail="No active payment session found"
            )
        
        # Check if this is a product pickup session
        if active_session.session_type == SessionType.PRODUCT_PICKUP:
            logger.info(f"Processing payment callback for product pickup session: {active_session.session_id}")

            # For product pickup sessions, we need to inject the callback directly into the pickup process
            # The pickup_process is waiting for payment_status message in its message queue
            from domains.product.websocket_handler import inject_payment_callback

            success = callback.status == "success"
            callback_message = {
                "type": "payment_status",
                "success": success,
                "message": callback.msg or ("Payment successful" if success else "Payment failed")
            }

            # Inject the callback message directly into the pickup process message queue
            result = await inject_payment_callback(active_session.session_id, callback_message)
            if not result:
                logger.error(f"Failed to inject payment callback for session {active_session.session_id}")
                raise HTTPException(
                    status_code=500,
                    detail="Failed to process product pickup payment callback"
                )

            logger.info(f"Injected payment callback to product pickup session: {active_session.session_id}")

            # Return a proper TransactionCallback for product pickup
            return TransactionCallback(
                transaction_id=active_session.session_id,  # Use session_id as transaction_id
                status=callback.status,
                type=callback.type,
                timestamp=datetime.now(),
                message="Payment callback processed for product pickup",
                data={
                    "session_type": "product_pickup",
                    "auth_code": callback.auth_code,
                    "terminal_timestamp": callback.t
                } if callback.auth_code or callback.t else {"session_type": "product_pickup"}
            )

        # Check if this is a product flow session
        elif active_session.session_type == SessionType.PRODUCT_FLOW:
            logger.info(f"Processing payment callback for product flow session: {active_session.session_id}")

            # Handle payment callback for product flow
            from domains.product.websocket_handler import handle_payment_callback

            result = await handle_payment_callback(
                session_id=active_session.session_id,
                status=callback.status,
                message=callback.msg
            )

            if not result:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to process product flow payment callback"
                )

            # Return a proper TransactionCallback for product flow
            return TransactionCallback(
                transaction_id=active_session.session_id,  # Use session_id as transaction_id
                status=callback.status,
                type=callback.type,
                timestamp=datetime.now(),
                message="Payment callback processed for product flow",
                data={
                    "session_type": "product_flow",
                    "auth_code": callback.auth_code,
                    "terminal_timestamp": callback.t
                } if callback.auth_code or callback.t else {"session_type": "product_flow"}
            )

        # Check if this is a storage flow session
        elif active_session.session_type == SessionType.STORAGE_FLOW:
            logger.info(f"Transaction router: Processing payment callback for storage flow session: {active_session.session_id}")

            # Handle payment callback for storage flow
            from domains.storage.websocket_handler import handle_payment_callback

            logger.info(f"Transaction router: Calling storage payment callback handler for session {active_session.session_id}")
            result = await handle_payment_callback(
                session_id=active_session.session_id,
                status=callback.status,
                message=callback.msg
            )
            logger.info(f"Transaction router: Storage payment callback result: {result}")

            if not result:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to process storage flow payment callback"
                )

            # Return a proper TransactionCallback for storage flow
            return TransactionCallback(
                transaction_id=active_session.session_id,  # Use session_id as transaction_id
                status=callback.status,
                type=callback.type,
                timestamp=datetime.now(),
                message="Payment callback processed for storage flow",
                data={
                    "session_type": "storage_flow",
                    "auth_code": callback.auth_code,
                    "terminal_timestamp": callback.t
                } if callback.auth_code or callback.t else {"session_type": "storage_flow"}
            )
        
        else:
            # Legacy transaction handling
            transaction_callback = TransactionCallback(
                transaction_id=active_session.transaction_data.get("transaction_id"),
                status=callback.status,
                type=callback.type,
                timestamp=datetime.now(),
                message=callback.msg,
                data={
                    "auth_code": callback.auth_code,
                    "terminal_timestamp": callback.t
                } if callback.auth_code or callback.t else None
            )
            
            logger.info(f"Processing transaction callback: {transaction_callback}")
            
            # Process callback
            success = await transaction_manager.handle_external_callback(transaction_callback)
            
            if not success:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to process transaction callback"
                )
            
        return transaction_callback
            
    except HTTPException:
        raise
    except Exception as err:
        logger.error(f"Error processing transaction callback: {err}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal error: {str(err)}"
        )