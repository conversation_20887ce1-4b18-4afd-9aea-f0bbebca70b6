"""
Order service for employment order management.
Contains business logic for order operations.
"""

import logging
from typing import Dict, Any, List
from uuid import uuid4

from infrastructure.repositories.order_repository import OrderRepository
from infrastructure.external_apis.jetveo_client import jetveo_client
from managers.session_manager import session_manager, SessionType
from managers.ws_manager import ws_manager

logger = logging.getLogger(__name__)

class OrderService:
    """Service for order operations"""
    
    def __init__(self):
        self.repo = OrderRepository()
        self.logger = logging.getLogger(__name__)
    
    async def pickup_expired_orders(self, operator_id: int) -> Dict[str, Any]:
        """
        Initialize pickup process for expired orders.
        """
        try:
            # Get all sections with expired orders
            sections = self.repo.get_expired_order_sections()
            total_sections = len(sections)
            
            if total_sections == 0:
                return {
                    "session_id": None,
                    "success": True,
                    "message": "No expired orders found",
                    "sections": [],
                    "total_sections": 0
                }
            
            # Create session for pickup process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="pickup_expired",
                operator_id=operator_id,
                sections=sections
            )
            
            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "message": "Failed to create session",
                    "sections": [],
                    "total_sections": 0
                }
            
            return {
                "session_id": session_id,
                "success": True,
                "message": "Expired orders found",
                "sections": sections,
                "total_sections": total_sections
            }
            
        except Exception as e:
            self.logger.error(f"Error in pickup_expired_orders: {e}")
            return {
                "session_id": None,
                "success": False,
                "message": f"Internal error: {str(e)}",
                "sections": [],
                "total_sections": 0
            }
    
    async def pickup_employee_orders(self, operator_id: int) -> Dict[str, Any]:
        """
        Initialize pickup process for employee orders.
        """
        try:
            # Get all sections with employee orders
            sections = self.repo.get_employee_order_sections()
            total_sections = len(sections)
            
            if total_sections == 0:
                return {
                    "session_id": None,
                    "success": True,
                    "message": "No employee orders found",
                    "sections": [],
                    "total_sections": 0
                }
            
            # Create session for pickup process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="pickup_employee",
                operator_id=operator_id,
                sections=sections
            )
            
            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "message": "Failed to create session",
                    "sections": [],
                    "total_sections": 0
                }
            
            return {
                "session_id": session_id,
                "success": True,
                "message": "Employee orders found",
                "sections": sections,
                "total_sections": total_sections
            }
            
        except Exception as e:
            self.logger.error(f"Error in pickup_employee_orders: {e}")
            return {
                "session_id": None,
                "success": False,
                "message": f"Internal error: {str(e)}",
                "sections": [],
                "total_sections": 0
            }
    
    async def deliver_to_employee(self, phone_number: str) -> Dict[str, Any]:
        """
        Initialize delivery process to employee.
        """
        try:
            # Check phone number validity with jetveo server
            validation_result = await jetveo_client.check_employment_deliver(phone_number)
            
            if not validation_result or not validation_result.get('valid', False):
                return {
                    "session_id": None,
                    "success": False,
                    "message": "Invalid phone number",
                    "section_ids": None
                }
            
            # Get reserved section_ids from jetveo response (if any)
            reserved_section_ids = validation_result.get('section_ids')

            # Create session for delivery process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="deliver_employee",
                phone_number=phone_number
            )

            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "message": "Failed to create session",
                    "section_ids": reserved_section_ids
                }

            return {
                "session_id": session_id,
                "success": True,
                "message": "Phone number validated, ready for delivery",
                "section_ids": reserved_section_ids
            }
            
        except Exception as e:
            self.logger.error(f"Error in deliver_to_employee: {e}")
            return {
                "session_id": None,
                "success": False,
                "message": f"Internal error: {str(e)}",
                "section_ids": None
            }
    
    async def employee_send_order(self, phone_number: str) -> Dict[str, Any]:
        """
        Initialize employee send order process.
        """
        try:
            # Check phone number validity with jetveo server
            validation_result = await jetveo_client.check_employment_send(phone_number)

            if not validation_result or not validation_result.get('valid', False):
                return {
                    "session_id": None,
                    "success": False,
                    "section_id": None,
                    "valid": False,
                    "message": "Invalid phone number"
                }

            # Get reserved section_id from jetveo response (if any)
            reserved_section_id = validation_result.get('section_id')

            # Create session for send process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="employee_send",
                phone_number=phone_number
            )

            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "section_id": reserved_section_id,
                    "valid": True,
                    "message": "Failed to create session"
                }

            return {
                "session_id": session_id,
                "success": True,
                "section_id": reserved_section_id,  # Return reserved section from jetveo
                "valid": True,
                "message": "Employee notified successfully"
            }

        except Exception as e:
            self.logger.error(f"Error in employee_send_order: {e}")
            return {
                "session_id": None,
                "success": False,
                "section_id": None,
                "valid": False,
                "message": f"Internal error: {str(e)}"
            }
    
    async def customer_pickup_order(self, pickup_pin: str) -> Dict[str, Any]:
        """
        Initialize customer pickup process.
        """
        try:
            # Find reservation by pickup PIN
            reservation = self.repo.find_reservation_by_pickup_pin(pickup_pin)
            
            if not reservation:
                return {
                    "session_id": None,
                    "success": False,
                    "message": f"Invalid pickup PIN: {pickup_pin}",
                    "section_id": None,
                    "requires_payment": False,
                    "amount": 0.0
                }
            
            # Create session for pickup process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="customer_pickup",
                reservation_id=reservation['id'],
                section_id=int(reservation['section_id'])
            )
            
            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "message": "Failed to create session",
                    "section_id": None,
                    "requires_payment": False,
                    "amount": 0.0
                }
            
            return {
                "session_id": session_id,
                "success": True,
                "message": "Order found, ready for pickup",
                "section_id": int(reservation['section_id']),
                "reservation_id": reservation['id'],
                "requires_payment": False,  # No payment required for orders
                "amount": 0.0
            }
            
        except Exception as e:
            self.logger.error(f"Error in customer_pickup_order: {e}")
            return {
                "session_id": None,
                "success": False,
                "message": f"Internal error: {str(e)}",
                "section_id": None,
                "requires_payment": False,
                "amount": 0.0
            }

    async def customer_reclaim_order(self, reclamation_pin: str) -> Dict[str, Any]:
        """
        Initialize customer reclaim order process.
        """
        try:
            # Check reclamation PIN validity with jetveo server
            validation_result = await jetveo_client.check_employment_reclaim(reclamation_pin)

            if not validation_result or not validation_result.get('valid', False):
                return {
                    "session_id": None,
                    "success": False,
                    "section_id": None,
                    "valid": False,
                    "message": "Invalid reclamation PIN"
                }

            # Get phone number and reserved section_id from jetveo response
            phone_number = validation_result.get('phone_number')
            reserved_section_id = validation_result.get('section_id')

            # Create session for reclaim process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="customer_reclaim",
                phone_number=phone_number
            )

            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "section_id": reserved_section_id,
                    "valid": True,
                    "message": "Failed to create session"
                }

            return {
                "session_id": session_id,
                "success": True,
                "section_id": reserved_section_id,  # Return reserved section from jetveo
                "valid": True,
                "phone_number": phone_number,  # Include phone number for flow config
                "message": "Employee notified successfully"
            }

        except Exception as e:
            self.logger.error(f"Error in customer_reclaim_order: {e}")
            return {
                "session_id": None,
                "success": False,
                "section_id": None,
                "valid": False,
                "message": f"Internal error: {str(e)}"
            }

    async def customer_send_order(self, reservation_pin: str) -> Dict[str, Any]:
        """
        Initialize customer send order process.
        Check if reservation exists with status=8 (ready for insert).
        """
        try:
            # Find reservation with status=8 (ready for insert)
            result = self.repo.find_reservation_by_pin_and_status(reservation_pin, 8)

            if not result["success"]:
                return {
                    "session_id": None,
                    "success": False,
                    "section_id": None,
                    "message": "Order not found or not ready for pickup"
                }

            reservation = result["reservation"]
            reserved_section_id = int(reservation["section_id"]) if reservation["section_id"] else None

            # Create session for customer send process
            session_id = str(uuid4())
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.ORDER_FLOW,
                operation="customer_send",
                reservation_id=reservation["id"],
                reservation_pin=reservation_pin
            )

            if not session:
                return {
                    "session_id": None,
                    "success": False,
                    "section_id": reserved_section_id,
                    "message": "Failed to create session"
                }

            return {
                "session_id": session_id,
                "success": True,
                "section_id": reserved_section_id,
                "message": "Order found, ready for pickup"
            }

        except Exception as e:
            self.logger.error(f"Error in customer_send_order: {e}")
            return {
                "session_id": None,
                "success": False,
                "section_id": None,
                "message": f"Internal error: {str(e)}"
            }

# Global service instance
order_service = OrderService()
