"""
Step Handlers for Storage Flow.
Each handler processes a specific step type (payment, hardware).
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod
import httpx
from managers.timeline_logger import log_timeline_event
from managers.transaction_logger import log_storage_transaction

from managers.sequence_manager import sequence_manager
from managers.ws_manager import ws_manager
from config import device_config
from infrastructure.repositories.storage_repository import StorageRepository

logger = logging.getLogger(__name__)

class StepHandler(ABC):
    """Base class for flow step handlers"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.websocket_connected = False
        self.waiting_for_ready = False
        self.ready_event = asyncio.Event()
        
    async def check_websocket_connection(self) -> bool:
        """Check if WebSocket is connected for this session"""
        self.websocket_connected = ws_manager.is_connected(self.session_id)
        if not self.websocket_connected:
            logger.warning(f"WebSocket not connected for session {self.session_id}")
        else:
            logger.info(f"WebSocket connected for session {self.session_id}")
        return self.websocket_connected
    
    async def send_websocket_message(self, message: Dict[str, Any]) -> bool:
        """Send JSON message via WebSocket"""
        if not await self.check_websocket_connection():
            return False
            
        try:
            await ws_manager.send(self.session_id, message)
            logger.info(f"Sent WebSocket message to {self.session_id}: {message}")
            return True
        except Exception as e:
            logger.error(f"Failed to send WebSocket message to {self.session_id}: {e}")
            return False
    
    async def wait_for_websocket_ready(self, screen_type: str, timeout: int = 60) -> bool:
        """Wait for the UI to signal screen readiness"""
        logger.info(f"Waiting for {screen_type}_screen_ready from session {self.session_id}")
        
        self.ready_event.clear()
        self.waiting_for_ready = True
        
        show_message = {
            "type": f"start_{screen_type}_screen",
            "wait_for_ready": True
        }
        
        if not await self.send_websocket_message(show_message):
            self.waiting_for_ready = False
            return False
        
        try:
            await asyncio.wait_for(self.ready_event.wait(), timeout=timeout)
            logger.info(f"Received {screen_type}_screen_ready for session {self.session_id}")
            return True
        except asyncio.TimeoutError:
            logger.error(f"Timeout waiting for {screen_type}_screen_ready from session {self.session_id} after {timeout}s")
            return False
        finally:
            self.waiting_for_ready = False
    
    def handle_screen_ready(self, screen_type: str) -> bool:
        """Handle screen ready signal from UI"""
        if self.waiting_for_ready:
            logger.info(f"Received {screen_type}_screen_ready for session {self.session_id}")
            self.ready_event.set()
            return True
        else:
            logger.warning(f"Received {screen_type}_screen_ready but not waiting for it in session {self.session_id}")
            return False
    
    @abstractmethod
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute the step"""
        pass

class PaymentHandler(StepHandler):
    """Handler for payment step"""
    
    def __init__(self, session_id: str):
        super().__init__(session_id)
        self.payment_completed = False
        self.payment_success = False
        self.section_id = None
        self.size_category = None
        self.amount = None
        self.payment_timeout_task = None
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute payment step using universal payment messages"""
        logger.info(f"Starting payment for session {self.session_id}")

        try:
            if not await self.wait_for_websocket_ready("payment"):
                return False

            self.amount = context.get('amount', 0)
            self.section_id = context.get('section_id')
            self.size_category = context.get('size_category')

            # Log transaction start
            log_storage_transaction(
                uuid=self.session_id,
                result="initiating",
                request={"type": "sale", "amount": self.amount, "variable_symbol": self.session_id}
            )
            log_timeline_event(
                event_type="new_transaction",
                event_result="payment_process_started",
                session_id=self.session_id,
                mode="storage"
            )

            # Send payment initiation status (like universal payment_process)
            await self.send_websocket_message({
                "type": "payment_status",
                "status": "initiating"
            })

            # Use universal payment terminal function
            from managers.process_manager import _process_payment_terminal
            payment_success = await _process_payment_terminal(self.session_id)

            if payment_success:
                # Send payment processing status (like universal payment_process)
                await self.send_websocket_message({
                    "type": "payment_status",
                    "status": "processing"
                })

                # Send payment result (like universal payment_process)
                await self.send_websocket_message({
                    "type": "payment_result",
                    "success": True,
                    "message": "Payment successful"
                })

                self.payment_completed = True
                self.payment_success = True

                # Log successful payment
                log_storage_transaction(
                    uuid=self.session_id,
                    result="success",
                    response={"status": "completed"}
                )
                log_timeline_event(
                    event_type="payment_callback",
                    event_result="payment_callback_success",
                    session_id=self.session_id,
                    mode="storage"
                )

                logger.info(f"Payment completed successfully for session {self.session_id}")
                return True
            else:
                # Send payment failed result
                await self.send_websocket_message({
                    "type": "payment_result",
                    "success": False,
                    "message": "Payment failed"
                })

                # Log failed payment
                log_storage_transaction(
                    uuid=self.session_id,
                    result="failed",
                    response={"status": "failed"}
                )
                log_timeline_event(
                    event_type="payment_callback",
                    event_result="payment_callback_error",
                    session_id=self.session_id,
                    mode="storage"
                )

                logger.error(f"Payment failed for session {self.session_id}")
                return False
                
        except Exception as e:
            logger.error(f"Payment error for session {self.session_id}: {e}")
            await self.send_websocket_message({"type": "payment_status", "status": "error", "error": str(e)})
            return False
    
    async def _payment_timeout_handler(self, timeout_seconds: int):
        try:
            await asyncio.sleep(timeout_seconds)
            if not self.payment_completed:
                self.payment_success = False
                self.payment_completed = True
                await self.send_websocket_message({"type": "payment_result", "success": False, "message": "Payment timed out"})
                from domains.storage.flow_coordinator import flow_coordinator
                await flow_coordinator.cleanup_flow(self.session_id)
        except asyncio.CancelledError:
            logger.info(f"Payment timeout task cancelled for session {self.session_id}")
        except Exception as e:
            logger.error(f"Error in payment timeout handler for session {self.session_id}: {e}")

    async def handle_payment_callback(self, status: str, message: str = ""):
        logger.info(f"Storage payment callback received for session {self.session_id}: status={status}, message={message}")

        if not self.payment_completed and self.payment_timeout_task and not self.payment_timeout_task.done():
            self.payment_timeout_task.cancel()

        if status == "success":
            self.payment_success = True
            self.payment_completed = True

            logger.info(f"Payment success for session {self.session_id}. section_id={self.section_id}, amount={self.amount}, size_category={self.size_category}")

            repo = StorageRepository()
            from managers.session_manager import session_manager
            session = session_manager.get_session(self.session_id)
            operation = getattr(session, "operation", None)

            logger.info(f"Session operation: {operation}, session found: {session is not None}")

            try:
                if operation == "storage_insert":
                    # Get values from session instead of instance variables
                    section_id = getattr(session, "section_id", None)
                    amount = getattr(session, "amount", None)
                    size_category = getattr(session, "size_category", None)
                    email = getattr(session, "email", None)

                    logger.info(f"Creating reservation with: section_id={section_id}, amount={amount}, size_category={size_category}, email={email}")

                    if not all([section_id, amount, size_category]):
                        raise ValueError(f"Missing required values: section_id={section_id}, amount={amount}, size_category={size_category}")

                    pin = repo.create_reservation(int(section_id), float(amount), int(size_category), email)
                    logger.info(f"Created storage reservation for section {section_id} with PIN {pin}")

                    # Store the reservation PIN in the session for later use
                    from managers.session_manager import session_manager
                    session_manager.update_session(self.session_id, reservation_pin=pin)

                    await self.send_websocket_message({"type": "payment_result", "success": True, "message": "Payment successful", "reservation_pin": pin})
                elif operation == "storage_pickup":
                    reservation_id = getattr(session, "reservation_id", None)
                    repo.deactivate_reservation(reservation_id)
                    logger.info(f"Deactivated storage reservation {reservation_id}")
                    await self.send_websocket_message({"type": "payment_result", "success": True, "message": "Payment successful"})
            except Exception as e:
                logger.error(f"Error processing storage operation after payment: {e}")
                await self.send_websocket_message({"type": "payment_result", "success": False, "message": "Error processing operation"})
            
        else:
            self.payment_success = False
            self.payment_completed = True
            await self.send_websocket_message({"type": "payment_result", "success": False, "message": f"Payment failed: {message}"})
    
    def is_completed(self) -> bool:
        return self.payment_completed
    
    def is_successful(self) -> bool:
        return self.payment_success

class HardwareHandler(StepHandler):
    """Handler for hardware operations step"""
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        logger.info(f"Starting hardware operation for session {self.session_id}")
        
        try:
            section_id = context.get('section_id')
            if not section_id:
                return False
            
            if not await self.wait_for_websocket_ready("hardware"):
                return False
            
            await self.send_websocket_message({"type": "hardware_status", "status": "preparing"})
            
            from domains.storage.service import storage_service
            # This needs to be implemented in storage_service
            hardware_config = await storage_service.get_hardware_config_for_section(int(section_id))
            
            if not hardware_config:
                await self.send_websocket_message({"type": "hardware_result", "success": False, "message": "Hardware config not found"})
                return False
            
            sections = [hardware_config]

            success = await sequence_manager.start_fsm_sequence(session_id=self.session_id, sections=sections, pin="0000") # Using a dummy PIN

            # After successful hardware operation, update the reservation status
            await self._complete_storage_operation()

            return True # Always return true as per requirement
                
        except Exception as e:
            logger.error(f"Hardware error for session {self.session_id}: {e}")
            await self.send_websocket_message({"type": "hardware_result", "success": False, "error": str(e)})
            return False

    async def _complete_storage_operation(self):
        """Complete the storage operation by updating the reservation status"""
        try:
            from managers.session_manager import session_manager
            session = session_manager.get_session(self.session_id)

            if not session:
                logger.error(f"Session not found for {self.session_id}")
                return

            operation = getattr(session, "operation", None)

            if operation == "storage_insert":
                # Get the reservation PIN from session
                reservation_pin = getattr(session, "reservation_pin", None)

                if reservation_pin:
                    repo = StorageRepository()
                    result = repo.complete_reservation(reservation_pin)

                    if result.get("success"):
                        logger.info(f"Successfully completed storage reservation {reservation_pin} for session {self.session_id}")
                        logger.info(f"Updated box status for section {result.get('section_id')} to occupied")
                        await self.send_websocket_message({
                            "type": "hardware_result",
                            "success": True,
                            "message": "Storage insertion completed successfully",
                            "reservation_uuid": result.get("reservation_uuid"),
                            "section_id": result.get("section_id")
                        })
                    else:
                        error_msg = result.get("error", "Unknown error")
                        logger.error(f"Failed to complete storage reservation {reservation_pin} for session {self.session_id}: {error_msg}")
                        await self.send_websocket_message({
                            "type": "hardware_result",
                            "success": False,
                            "message": f"Failed to complete storage reservation: {error_msg}"
                        })
                else:
                    logger.error(f"No reservation PIN found in session {self.session_id}")
                    await self.send_websocket_message({
                        "type": "hardware_result",
                        "success": False,
                        "message": "No reservation PIN found"
                    })

            elif operation == "storage_pickup":
                # For pickup operations, deactivate the reservation if not already done
                reservation_id = getattr(session, "reservation_id", None)
                if reservation_id:
                    from infrastructure.repositories.storage_repository import StorageRepository
                    repo = StorageRepository()
                    success = repo.deactivate_reservation(reservation_id)
                    if success:
                        logger.info(f"Deactivated storage reservation {reservation_id} after pickup")
                    else:
                        logger.warning(f"Failed to deactivate storage reservation {reservation_id}")

                logger.info(f"Storage pickup hardware operation completed for session {self.session_id}")
                await self.send_websocket_message({
                    "type": "hardware_result",
                    "success": True,
                    "message": "Storage pickup completed successfully"
                })

        except Exception as e:
            logger.error(f"Error completing storage operation for session {self.session_id}: {e}")
            await self.send_websocket_message({
                "type": "hardware_result",
                "success": False,
                "message": f"Error completing operation: {str(e)}"
            })

# PickupLoopHandler removed - now using pickup_process directly in WebSocket handler





# _complete_storage_pickup_operation removed - now handled directly in WebSocket handler

def create_step_handler(step_type: str, session_id: str) -> Optional[StepHandler]:
    """Create appropriate step handler based on step type"""
    handlers = {
        "payment": PaymentHandler,
        "hardware": HardwareHandler,
        # pickup_loop removed - now using pickup_process directly in WebSocket handler
    }
    handler_class = handlers.get(step_type)
    if handler_class:
        return handler_class(session_id)
    return None
