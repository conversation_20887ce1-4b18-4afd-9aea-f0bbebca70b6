"""
Order API Router.
Handles employment order management endpoints.
"""

import logging
from fastapi import APIRouter, HTTPException
from typing import Dict, Any

from domains.order.models import (
    EmploymentPickupExpiredRequest, EmploymentPickupExpiredResponse,
    EmploymentPickupRequest, EmploymentPickupResponse,
    EmploymentDeliverRequest, EmploymentDeliverResponse,
    EmploymentSendRequest, EmploymentSendResponse,
    CustomerPickupRequest, CustomerPickupResponse,
    CustomerReclaimRequest, CustomerReclaimResponse,
    CustomerSendRequest, CustomerSendResponse
)
from domains.order.service import order_service
from domains.order.flow_coordinator import flow_coordinator
from managers.timeline_logger import log_timeline_event

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/employment/courier/pickup-expired", response_model=EmploymentPickupExpiredResponse)
async def pickup_expired_orders(request: EmploymentPickupExpiredRequest):
    """
    Request to pickup expired orders.
    Creates a WebSocket session and starts pickup loop if expired orders are found.
    """
    try:
        result = await order_service.pickup_expired_orders(request.operator_id)
        
        # If we have sections to pickup, start the flow
        if result["success"] and result["total_sections"] > 0:
            flow_config = {
                "operation": "pickup_expired",
                "sections": result["sections"],
                "operator_id": request.operator_id
            }
            
            flow_started = await flow_coordinator.start_flow(result["session_id"], flow_config)
            
            if not flow_started:
                return EmploymentPickupExpiredResponse(
                    session_id=None,
                    success=False,
                    message="Failed to start pickup flow",
                    sections=[],
                    total_sections=0
                )
        
        return EmploymentPickupExpiredResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in pickup_expired_orders: {e}")
        log_timeline_event(
            event_type="pickup_expired",
            event_result="failed",
            operator_id=request.operator_id,
            message=f"Error in pickup_expired_orders: {str(e)}",
            mode="order"
        )
        return EmploymentPickupExpiredResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0
        )

@router.post("/employment/courier/pickup", response_model=EmploymentPickupResponse)
async def pickup_employee_orders(request: EmploymentPickupRequest):
    """
    Function for courier to pickup orders from employees.
    Creates a WebSocket session and starts pickup loop if employee orders are found.
    """
    try:
        result = await order_service.pickup_employee_orders(request.operator_id)
        
        # If we have sections to pickup, start the flow
        if result["success"] and result["total_sections"] > 0:
            flow_config = {
                "operation": "pickup_employee",
                "sections": result["sections"],
                "operator_id": request.operator_id
            }
            
            flow_started = await flow_coordinator.start_flow(result["session_id"], flow_config)
            
            if not flow_started:
                return EmploymentPickupResponse(
                    session_id=None,
                    success=False,
                    message="Failed to start pickup flow",
                    sections=[],
                    total_sections=0
                )
        
        return EmploymentPickupResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in pickup_employee_orders: {e}")
        log_timeline_event(
            event_type="pickup_employee",
            event_result="failed",
            operator_id=request.operator_id,
            message=f"Error in pickup_employee_orders: {str(e)}",
            mode="order"
        )
        return EmploymentPickupResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0
        )

@router.post("/employment/courier/deliver", response_model=EmploymentDeliverResponse)
async def deliver_to_employee(request: EmploymentDeliverRequest):
    """
    Function to deliver orders to employees. This function is for courier.
    Validates phone number and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.deliver_to_employee(request.phone_number)
        
        # If phone number is valid, start the flow
        if result["success"]:
            flow_config = {
                "operation": "deliver_employee",
                "phone_number": request.phone_number,
                "reserved_section_ids": result.get("section_ids")
            }
            
            flow_started = await flow_coordinator.start_flow(result["session_id"], flow_config)
            
            if not flow_started:
                return EmploymentDeliverResponse(
                    session_id=None,
                    success=False,
                    message="Failed to start delivery flow",
                    section_ids=result.get("section_ids")
                )
        
        return EmploymentDeliverResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in deliver_to_employee: {e}")
        log_timeline_event(
            event_type="deliver_employee",
            event_result="failed",
            message=f"Error in deliver_to_employee: {str(e)}",
            mode="order"
        )
        return EmploymentDeliverResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            section_ids=None
        )

@router.post("/employment/customer/send", response_model=EmploymentSendResponse)
async def employee_send_order(request: EmploymentSendRequest):
    """
    Function for employee to send order.
    Validates phone number and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.employee_send_order(request.phone_number)
        
        # If phone number is valid, start the flow
        if result["success"] and result["valid"]:

            log_timeline_event(
                event_type="employee_send",
                event_result="started",
                phone_number=request.phone_number,
                message="Employee send order started",
                mode="order"
            )

            flow_config = {
                "operation": "employee_send",
                "phone_number": request.phone_number,
                "reserved_section_id": result.get("section_id")
            }
            
            flow_started = await flow_coordinator.start_flow(result["session_id"], flow_config)
            
            if not flow_started:
                return EmploymentSendResponse(
                    session_id=None,
                    success=False,
                    section_id=None,
                    valid=result["valid"],
                    message="Failed to start send flow"
                )
        
        elif not result["valid"]:
            log_timeline_event(
                event_type="employee_send",
                event_result="phone_number_not_found",
                phone_number=request.phone_number,
                message="Invalid phone number",
                mode="order"
            )

        return EmploymentSendResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in employee_send_order: {e}")
        log_timeline_event(
            event_type="employee_send",
            event_result="failed",
            phone_number=request.phone_number,
            message=f"Error in employee_send_order: {str(e)}",
            mode="order"
        )
        return EmploymentSendResponse(
            session_id=None,
            success=False,
            section_id=None,
            valid=False,
            message=f"Internal server error: {str(e)}"
        )

@router.post("/customer/pickup", response_model=CustomerPickupResponse)
async def customer_pickup_order(request: CustomerPickupRequest):
    """
    Function for customer to pickup order using PIN.
    Similar to product pickup - creates WebSocket session and waits for hardware_screen_ready.
    """
    try:
        result = await order_service.customer_pickup_order(request.pickup_pin)
        
        # If order is found, start the flow
        if result["success"]:
            flow_config = {
                "operation": "customer_pickup",
                "section_id": result["section_id"],
                "reservation_id": result.get("reservation_id")
            }

            log_timeline_event(
                event_type="pin_entered",
                event_result="order_found",
                entered_pin=request.pickup_pin,
                message="Customer pickup started",
                mode="order"
            )
            
            flow_started = await flow_coordinator.start_flow(result["session_id"], flow_config)
            
            if not flow_started:
                return CustomerPickupResponse(
                    session_id=None,
                    success=False,
                    message="Failed to start pickup flow",
                    section_id=None,
                    requires_payment=False,
                    amount=0.0
                )
        else:
            log_timeline_event(
                event_type="pin_entered",
                event_result="order_not_found",
                entered_pin=request.pickup_pin,
                message="Invalid pickup PIN",
                mode="order"
            )
        
        return CustomerPickupResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in customer_pickup_order: {e}")
        log_timeline_event(
            event_type="pin_entered",
            event_result="failed",
            entered_pin=request.pickup_pin,
            message=f"Error in customer_pickup_order: {str(e)}",
            mode="order"
        )
        return CustomerPickupResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            section_id=None,
            requires_payment=False,
            amount=0.0
        )

@router.post("/employment/customer/reclaim", response_model=CustomerReclaimResponse)
async def customer_reclaim_order(request: CustomerReclaimRequest):
    """
    Function for customer to reclaim order using reclamation PIN.
    Validates reclamation PIN and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.customer_reclaim_order(request.reclamation_pin)

        # If reclamation PIN is valid, start the flow
        if result["success"] and result["valid"]:
            flow_config = {
                "operation": "customer_reclaim",
                "phone_number": result.get("phone_number"),
                "reserved_section_id": result.get("section_id")
            }

            log_timeline_event(
                event_type="reclamation_pin_entered",
                event_result="valid_pin",
                entered_pin=request.reclamation_pin,
                message="Reclamation PIN is valid",
                mode="order"
            )

            flow_started = await flow_coordinator.start_flow(result["session_id"], flow_config)

            if not flow_started:
                return CustomerReclaimResponse(
                    session_id=None,
                    success=False,
                    section_id=result.get("section_id"),
                    valid=result["valid"],
                    message="Failed to start reclaim flow"
                )
            
        else:
            log_timeline_event(
                event_type="pin_entered",
                event_result="invalid_pin",
                entered_pin=request.reclamation_pin,
                message="Invalid reclamation PIN",
                mode="order"
            )

        return CustomerReclaimResponse(**result)

    except Exception as e:
        logger.error(f"Error in customer_reclaim_order: {e}")
        log_timeline_event(
            event_type="reclamation_pin_entered",
            event_result="failed",
            entered_pin=request.reclamation_pin,
            message=f"Error in customer_reclaim_order: {str(e)}",
            mode="order"
        )
        return CustomerReclaimResponse(
            session_id=None,
            success=False,
            section_id=None,
            valid=False,
            message=f"Internal server error: {str(e)}"
        )

@router.post("/customer/send", response_model=CustomerSendResponse)
async def customer_send_order(request: CustomerSendRequest):
    """
    Function for customer to send order to courier using reservation PIN.
    Checks if reservation exists with status=8 (ready for insert) and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.customer_send_order(request.reservation_pin)

        # If order is found, start the flow
        if result["success"]:
            flow_config = {
                "operation": "customer_send",
                "reservation_pin": request.reservation_pin,
                "reserved_section_id": result.get("section_id")
            }

            flow_started = await flow_coordinator.start_flow(result["session_id"], flow_config)

            if not flow_started:
                return CustomerSendResponse(
                    session_id=None,
                    success=False,
                    section_id=result.get("section_id"),
                    message="Failed to start send flow"
                )

            log_timeline_event(
                event_type="customer_send_started",
                event_result="success",
                entered_pin=str(request.reservation_pin),
                message="Customer send order started",
                mode="order"
            )

        return CustomerSendResponse(**result)

    except Exception as e:
        logger.error(f"Error in customer_send_order: {e}")
        log_timeline_event(
            event_type="customer_send",
            event_result="failed",
            message=f"Error in customer_send_order: {str(e)}",
            mode="order"
        )
        return CustomerSendResponse(
            session_id=None,
            success=False,
            section_id=None,
            message=f"Internal server error: {str(e)}"
        )
