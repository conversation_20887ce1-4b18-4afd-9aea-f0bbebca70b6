"""
WebSocket handler for order operations.
Handles order pickup using pickup_process and order selection using select_sections directly.
"""

import json
import logging
import asyncio
from fastapi import WebSocket, WebSocketDisconnect
from managers import ws_manager, session_manager
from infrastructure.repositories.order_repository import OrderRepository

logger = logging.getLogger(__name__)


async def handle_order_pickup_websocket(websocket: WebSocket, session_id: str):
    """
    Handle WebSocket connection for order pickup operations.
    Uses pickup_process directly as defined in screen_communication.md

    Args:
        websocket: FastAPI WebSocket connection
        session_id: Session ID for this connection
    """
    logger.info(f"Order pickup WebSocket handler started for session: {session_id}")

    try:
        # Register WebSocket connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Order pickup WebSocket connected: {session_id}")

        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Session not found"
            })
            return

        # Get sections and operation details from session
        sections = getattr(session, 'sections', [])
        operation = getattr(session, 'operation', 'unknown')
        operator_id = getattr(session, 'operator_id', None)
        section_id = getattr(session, 'section_id', None)
        reservation_id = getattr(session, 'reservation_id', None)

        # For single section operations, convert to list
        if section_id and not sections:
            sections = [section_id]

        if not sections:
            logger.error(f"No sections found in session {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "No sections found"
            })
            return
        
        # Create message queue for pickup_process
        message_queue = asyncio.Queue()

        # Message handler to route WebSocket messages to pickup_process
        async def handle_websocket_messages():
            while ws_manager.is_connected(session_id):
                try:
                    message = await websocket.receive_text()
                    if not message or message.strip() == "":
                        continue

                    try:
                        data = json.loads(message)
                    except json.JSONDecodeError:
                        await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                        continue

                    msg_type = data.get("type")

                    if msg_type == "ping":
                        await ws_manager.send(session_id, {"type": "pong"})
                        continue

                    # Route message to pickup_process
                    await message_queue.put(data)

                except WebSocketDisconnect:
                    logger.info(f"Order pickup WebSocket disconnected: {session_id}")
                    break
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {e}")
                    break

        # Start message handler task
        message_task = asyncio.create_task(handle_websocket_messages())

        # Start pickup_process directly from universal process_manager
        from managers.process_manager import pickup_process
        success, successful_sections = await pickup_process(
            sections=sections,
            session_id=session_id,
            message_queue=message_queue,
            requires_payment=False  # Orders don't require payment
        )

        # Cancel message handler
        message_task.cancel()

        # Complete order pickup operation
        if success and successful_sections:
            repo = OrderRepository()

            # Update order reservations based on operation type
            if operation in ["pickup_expired", "pickup_employee"]:
                # Update multiple sections
                for section in successful_sections:
                    repo.update_reservation_status_by_section(section, 0)  # Set status to 0 (completed)
                logger.info(f"Updated {len(successful_sections)} order reservations after pickup")

            elif operation == "customer_pickup" and reservation_id:
                # Update single reservation
                repo.update_reservation_status(reservation_id, 0)  # Set status to 0 (completed)
                logger.info(f"Updated order reservation {reservation_id} after pickup")

        logger.info(f"Order pickup completed for session {session_id}: success={success}")

    except Exception as e:
        logger.error(f"Error in order pickup WebSocket handler: {e}")
    finally:
        # Clean up
        ws_manager.disconnect(session_id)
        logger.info(f"Order pickup WebSocket handler ended for session: {session_id}")


async def handle_order_flow_websocket(websocket: WebSocket, session_id: str):
    """
    Handle WebSocket connection for order operations.
    Routes to appropriate handler based on operation type.
    """
    # Get session to determine operation type
    session = session_manager.get_session(session_id)
    if not session:
        logger.error(f"No session found for {session_id}")
        return

    operation = getattr(session, 'operation', 'unknown')
    logger.info(f"Order WebSocket routing: session_id={session_id}, operation={operation}")

    # Route to appropriate handler based on operation
    if operation in ["pickup_expired", "pickup_employee", "customer_pickup"]:
        logger.info(f"Routing to pickup handler for operation: {operation}")
        await handle_order_pickup_websocket(websocket, session_id)
    else:
        logger.info(f"Routing to flow handler for operation: {operation}")
        # For other operations, keep using flow-based approach for now
        try:
            from .flow_coordinator import flow_coordinator
        except ImportError as e:
            logger.error(f"Failed to import flow_coordinator: {e}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Flow coordinator not available"
            })
            return

        logger.info(f"Order flow WebSocket handler started for session: {session_id}")

        try:
            await ws_manager.connect(session_id, websocket)
            logger.info(f"Registered WebSocket connection for order flow session: {session_id}")

            # Get flow status
            flow_status = await flow_coordinator.get_flow_status(session_id)

            if not flow_status.get("exists", False):
                await ws_manager.send(session_id, {
                    "type": "error",
                    "message": "Order flow not found for this session"
                })
                return

            # Send initial flow status
            await ws_manager.send(session_id, {
                "type": "flow_status",
                "status": "connected",
                "current_step": flow_status.get("current_step"),
                "message": "Připojeno k order flow"
            })

            # Start flow execution
            asyncio.create_task(flow_coordinator.execute_current_step_async(session_id))

            while ws_manager.is_connected(session_id):
                try:
                    message = await websocket.receive_text()
                    if not message or message.strip() == "":
                        continue

                    try:
                        data = json.loads(message)
                    except json.JSONDecodeError:
                        await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                        continue

                    msg_type = data.get("type")

                    if msg_type == "ping":
                        await ws_manager.send(session_id, {"type": "pong"})
                        continue

                    success = await flow_coordinator.handle_websocket_message(session_id, data)

                    if not success:
                        await ws_manager.send(session_id, {
                            "type": "message_error",
                            "message": f"Failed to handle message type '{msg_type}'"
                        })

                except WebSocketDisconnect:
                    break
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {e}")
                    await ws_manager.send(session_id, {"type": "error", "message": str(e)})

        except WebSocketDisconnect:
            logger.info(f"Order flow WebSocket disconnected: {session_id}")
        except Exception as e:
            logger.error(f"Unexpected error in order flow WebSocket handler: {e}")
        finally:
            ws_manager.disconnect(session_id)
            await flow_coordinator.cleanup_flow(session_id)
            logger.info(f"Order flow WebSocket connection closed: {session_id}")
