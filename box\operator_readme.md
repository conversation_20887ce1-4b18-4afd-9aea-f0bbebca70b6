

**Request Body:**
```json
{
    "pin": "ABCDEF1234"  // PIN code (9 or 10 characters)
}
```

**Response Body (Success):**
```json
{
    "grand": "allow",
    "type": "manager",           // operator type: manager, service, hygiene, courier, montage
    "operator_id": 1,            // operator ID (for 9-char PINs from server)
    "name": "<PERSON>",          // operator name (for 9-char <PERSON><PERSON>s from server)
    "session_id": "operator_1a219d3e-ed9b-411a-92c3-96c11b0bfda5",
    "websocket_url": "/ws/operator_1a219d3e-ed9b-411a-92c3-96c11b0bfda5",
    "message": "Operator session created",
    "expires_at": "2024-01-01T12:00:00",
    "close": false,
    "permissions": {
        "service_open": {"allowed": true, "sections": [1,2,3,4,5]},
        "collection": {"allowed": true, "sections": [2,4]},
        "product_insert": {"allowed": true, "sections": [1,3,5]},
        "manager_mode": {"allowed": true},
        "delivery": {"allowed": true}
    }
}
```

**Response Body (Denied):**
```json
{
    "grand": "deny",
    "type": "unknown"           // types: unknown, server_denied, server_error, server_timeout, internal_error, invalid_length
}
```


### Operator Commands

#### Open Sections
```json
// Request
{
    "type": "operator_unlock",
    "sections": "1",
    "wait_for_close": true
}

// Response (success)
{
    "type": "operator_sequence_started",
    "sections": [1, 2, 3],
    "message": "Starting opening of 3 sections"
}
.... a dalsi informace

// Response (error)
{
    "type": "error",
    "message": "No valid sections to open"
}
```

