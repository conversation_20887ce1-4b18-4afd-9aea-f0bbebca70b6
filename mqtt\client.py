import json
import os
import asyncio
import threading
import mysql.connector
from typing import Optional, Callable, Dict, Any
from dotenv import load_dotenv
import paho.mqtt.client as mqtt
import logging

# Import hardware control
from hardware.locker_control import Locker<PERSON>ontroller
from hardware.electronics_api import send_command
from infrastructure.external_apis.jetveo_client import storage_change_status_async


# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class MQTTClient:
    """MQTT Client for handling device communication"""

    def __init__(self):
        # MQTT Configuration from environment
        self.mqtt_enabled = os.getenv("MQTT_ENABLE", "false").lower() in ("true", "1", "yes", "on")
        self.broker = os.getenv("MQTT_HOST")
        self.port = int(os.getenv("MQTT_PORT", 1883))
        self.username = os.getenv("MQTT_USERNAME")
        self.password = os.getenv("MQTT_PASSWORD")
        self.client_id = os.getenv("MQTT_CLIENT_ID", "python-client")

        # Topic patterns for different command types
        self.base_topic = f"devices/{self.client_id}/commands"
        self.base_response_topic = f"devices/{self.client_id}/responses"

        # Command topics
        self.electronic_topics = [
            f"{self.base_topic}/electronic/section_open",
            f"{self.base_topic}/electronic/check_doors",
            f"{self.base_topic}/electronic/unlock_service",
            f"{self.base_topic}/electronic/check_service"
        ]

        self.system_topics = [
            f"{self.base_topic}/system/reboot_device"
        ]

        self.sale_topics = [
            f"{self.base_topic}/sale/edit_reservation",
            f"{self.base_topic}/sale/reserve_product",
            f"{self.base_topic}/sale/unreserve_product"
        ]

        self.storage_topics = [
            f"{self.base_topic}/storage/edit_reservation"
        ]

        # All command topics
        self.all_command_topics = (
            self.electronic_topics +
            self.system_topics +
            self.sale_topics +
            self.storage_topics
        )

        # MQTT client instance
        self.client: Optional[mqtt.Client] = None
        self.is_connected = False
        self.is_running = False
        self.loop_thread: Optional[threading.Thread] = None

        # Message handlers
        self.message_handlers: Dict[str, Callable] = {}

        # Hardware controller for electronic commands
        self.locker_controller = LockerController()

        # Database connection - reuse instead of creating new connections each time
        self.conn = None
        self._setup_database_connection()

    def _setup_database_connection(self):
        """Setup database connection that will be reused"""
        try:
            self.conn = mysql.connector.connect(
                host=os.getenv("DB_HOST"),
                port=int(os.getenv("DB_PORT", 3306)),
                database=os.getenv("DB_NAME"),
                user=os.getenv("DB_USER"),
                password=os.getenv("DB_PASSWORD"),
                autocommit=True,
                pool_reset_session=True
            )
            logger.info("Database connection established for MQTT client")
        except Exception as e:
            logger.error(f"Failed to establish database connection: {e}")
            self.conn = None

    def _ensure_db_connection(self):
        """Ensure database connection is active, reconnect if needed"""
        try:
            if self.conn is None or not self.conn.is_connected():
                self._setup_database_connection()
        except Exception as e:
            logger.error(f"Database connection check failed: {e}")
            self._setup_database_connection()

    def setup_client(self):
        """Initialize MQTT client with callbacks"""
        if self.client is not None:
            return
            
        self.client = mqtt.Client(client_id=self.client_id)
        self.client.username_pw_set(self.username, self.password)
        self.client.on_connect = self._on_connect
        self.client.on_message = self._on_message
        self.client.on_disconnect = self._on_disconnect
        
    def _on_connect(self, client, userdata, flags, rc):
        """Callback for when the client connects to the broker"""
        if rc == 0:
            self.is_connected = True
            logger.info("Connected to MQTT broker")

            # Subscribe to all command topics
            for topic in self.all_command_topics:
                client.subscribe(topic)
                logger.info(f"Subscribed to {topic}")
        else:
            self.is_connected = False
            logger.error(f"Failed to connect to MQTT broker, return code: {rc}")
    
    def _on_disconnect(self, client, userdata, rc):
        """Callback for when the client disconnects from the broker"""
        self.is_connected = False
        logger.info("Disconnected from MQTT broker")
        
    def _on_message(self, client, userdata, msg):
        """Callback for when a message is received"""
        try:
            payload = json.loads(msg.payload.decode("utf-8"))
            logger.info(f"Received message on {msg.topic}: {payload}")

            # Route message to appropriate handler based on topic
            response = self._handle_command(msg.topic, payload)

            # Send response back to the corresponding response topic
            if response:
                self.publish_response(msg.topic, response)

            # Call custom message handlers if any
            for topic_pattern, handler in self.message_handlers.items():
                if msg.topic == topic_pattern or topic_pattern == "*":
                    try:
                        handler(msg.topic, payload)
                    except Exception as e:
                        logger.error(f"Error in message handler: {e}")

        except json.JSONDecodeError:
            logger.error(f"Failed to decode JSON: {msg.payload}")
        except Exception as e:
            logger.error(f"Error processing message: {e}")

    def _handle_command(self, topic: str, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Handle different command types based on topic"""
        try:
            # Extract command type from topic
            topic_parts = topic.split('/')
            if len(topic_parts) < 4:
                logger.error(f"Invalid topic format: {topic}")
                return {"error": "Invalid topic format"}

            command_category = topic_parts[-2]  # electronic, system, sale, storage
            command_name = topic_parts[-1]     # section_open, check_doors, etc.

            logger.info(f"Processing {command_category}/{command_name} command")

            # Route to appropriate handler
            if command_category == "electronic":
                return self._handle_electronic_command(command_name, payload)
            elif command_category == "system":
                return self._handle_system_command(command_name, payload)
            elif command_category == "sale":
                return self._handle_sale_command(command_name, payload)
            elif command_category == "storage":
                return self._handle_storage_command(command_name, payload)
            else:
                logger.error(f"Unknown command category: {command_category}")
                return {"error": f"Unknown command category: {command_category}"}

        except Exception as e:
            logger.error(f"Error handling command: {e}")
            return {"error": str(e)}

    def _handle_electronic_command(self, command_name: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle electronic commands"""
        logger.info(f"Electronic command: {command_name}")

        if command_name == "section_open":
            return self._section_open(payload)
        elif command_name == "check_doors":
            return self._check_doors(payload)
        elif command_name == "unlock_service":
            return self._unlock_service(payload)
        elif command_name == "check_service":
            return self._check_service(payload)
        else:
            logger.error(f"Unknown electronic command: {command_name}")
            return {"error": f"Unknown electronic command: {command_name}"}

    def _section_open(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Open a specific section"""
        section_id = payload.get("section_id")
        if not section_id:
            return {"success": False, "error": "section_id is required"}

        try:
            logger.info(f"Opening section {section_id}")
            # Use asyncio to run the async unlock function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                success = loop.run_until_complete(
                    self.locker_controller.unlock_locker(section_id, is_tempered=True, mode="mqtt")
                )
                if success:
                    return {"success": True}
                else:
                    return {"success": False, "error": f"Hardware failed to unlock section {section_id}"}
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"Error opening section {section_id}: {e}")
            return {"success": False, "error": f"Exception while opening section {section_id}: {str(e)}"}

    def _check_doors(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Check door state for a specific section"""
        section_id = payload.get("section_id")
        if not section_id:
            return {"error": "section_id is required"}

        try:
            logger.info(f"Checking door state for section {section_id}")
            # Use asyncio to run the async check function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                door_state = loop.run_until_complete(
                    self.locker_controller.check_door_state(section_id, is_tempered=True)
                )
                if door_state is None:
                    return {"error": f"Hardware failed to read door state for section {section_id}"}
                return {"door_state": "open" if door_state else "closed"}
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"Error checking door state for section {section_id}: {e}")
            return {"error": f"Exception while checking door state for section {section_id}: {str(e)}"}

    def _unlock_service(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Unlock service door"""
        try:
            logger.info("Unlocking service door")
            # Use asyncio to run the async service unlock function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    send_command("", "unlock_service", max_retries=2)
                )
                success = not result.startswith('-')
                if success:
                    return {"success": True}
                else:
                    return {"success": False, "error": f"Hardware failed to unlock service door, result code: {result}"}
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"Error unlocking service door: {e}")
            return {"success": False, "error": f"Exception while unlocking service door: {str(e)}"}

    def _check_service(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Check service door state"""
        try:
            logger.info("Checking service door state")
            # Use asyncio to run the async service check function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    send_command("", "read_service_lock_state", max_retries=2)
                )
                if result.startswith('-'):
                    return {"error": f"Hardware failed to read service state, result code: {result}"}

                # Parse result - expecting format like "1 0 1" where middle value is service state
                parts = result.strip().split()
                if len(parts) >= 3:
                    service_state = "unlocked" if parts[1] == "1" else "locked"
                    return {"service_state": service_state}
                else:
                    return {"error": f"Invalid service state response format: {result}"}
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"Error checking service door state: {e}")
            return {"error": f"Exception while checking service door state: {str(e)}"}

    def _handle_system_command(self, command_name: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle system commands"""
        logger.info(f"System command: {command_name}")

        if command_name == "reboot_device":
            return self._reboot_device(payload)
        else:
            logger.error(f"Unknown system command: {command_name}")
            return {"error": f"Unknown system command: {command_name}"}

    def _reboot_device(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Reboot device - placeholder function"""
        logger.info("Device reboot function called (placeholder)")
        # TODO: Implement actual reboot functionality
        # This could involve:
        # - Graceful shutdown of services
        # - System reboot command
        # - Hardware reset
        return {"success": True}

    def _handle_sale_command(self, command_name: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle sale commands"""
        logger.info(f"Sale command: {command_name}")

        if command_name == "edit_reservation":
            return self._edit_sale_reservation(payload)
        elif command_name == "reserve_product":
            return self._reserve_product(payload)
        elif command_name == "unreserve_product":
            return self._unreserve_product(payload)
        else:
            logger.error(f"Unknown sale command: {command_name}")
            return {"error": f"Unknown sale command: {command_name}"}

    def _edit_sale_reservation(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Edit sale reservation in database"""
        uuid = payload.get("uuid")
        section_id = payload.get("section_id")
        status = payload.get("status")
        price = payload.get("price")

        if not uuid and not section_id:
            return {"success": False, "error": "uuid or section_id is required"}

        logger.info(f"Editing sale reservation uuid={uuid}, section_id={section_id}: status={status}, price={price}")

        try:
            success = self._update_sale_reservation_new(uuid, section_id, status, price)
            if success:
                return {"success": True}
            else:
                identifier = uuid if uuid else f"section_id {section_id}"
                return {"success": False, "error": f"Sale reservation with {identifier} not found in database"}
        except Exception as e:
            logger.error(f"Error updating sale reservation: {e}")
            identifier = uuid if uuid else f"section_id {section_id}"
            return {"success": False, "error": f"Database error while updating sale reservation {identifier}: {str(e)}"}

    def _reserve_product(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Reserve product - set reservation to 1 and generate reservation pin

        Checks if product is already reserved before attempting to reserve it.
        Returns success=false if product is already reserved.
        """
        uuid = payload.get("uuid")
        section_id = payload.get("section_id")

        if not uuid and not section_id:
            return {"success": False, "error": "uuid or section_id is required"}

        logger.info(f"Reserving product uuid={uuid}, section_id={section_id}")

        try:
            reservation_pin = self._reserve_sale_product(uuid, section_id)
            if reservation_pin:
                return {"success": True, "reservation_pin": reservation_pin}
            else:
                identifier = uuid if uuid else f"section_id {section_id}"
                # Check if it's because the product is already reserved
                success = self._check_if_already_reserved(uuid, section_id)
                if success:
                    return {"success": False, "error": f"Product {identifier} is already reserved"}
                else:
                    return {"success": False, "error": f"Product {identifier} not found or unavailable for reservation"}
        except Exception as e:
            logger.error(f"Error reserving product: {e}")
            identifier = uuid if uuid else f"section_id {section_id}"
            return {"success": False, "error": f"Database error while reserving product {identifier}: {str(e)}"}

    def _check_if_already_reserved(self, uuid: str, section_id: int) -> bool:
        """Check if a product is already reserved"""
        try:
            # Ensure database connection
            self._ensure_db_connection()
            if not self.conn:
                return False

            cursor = self.conn.cursor()

            # Build query based on available parameters
            if uuid:
                where_clause = "uuid = %s"
                where_param = uuid
            else:
                where_clause = "section_id = %s"
                where_param = section_id

            # Check if reservation exists and is already reserved
            query = f"SELECT reserved FROM sale_reservations WHERE {where_clause} AND status = 1"
            cursor.execute(query, (where_param,))
            result = cursor.fetchone()

            cursor.close()

            # Return True if product exists and is already reserved
            return result and result[0] == 1

        except Exception as e:
            logger.error(f"Error checking reservation status: {e}")
            return False

    def _unreserve_product(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Unreserve product - set reservation to 0 and delete reservation pin"""
        uuid = payload.get("uuid")
        section_id = payload.get("section_id")
        reservation_pin = payload.get("reservation_pin")

        if not uuid and not section_id and not reservation_pin:
            return {"success": False, "error": "uuid, section_id, or reservation_pin is required"}

        logger.info(f"Unreserving product uuid={uuid}, section_id={section_id}, reservation_pin={reservation_pin}")

        try:
            success = self._unreserve_sale_product(uuid, section_id, reservation_pin)
            if success:
                return {"success": True}
            else:
                identifier = uuid if uuid else (f"section_id {section_id}" if section_id else f"reservation_pin {reservation_pin}")
                return {"success": False, "error": f"Sale reservation with {identifier} not found in database"}
        except Exception as e:
            logger.error(f"Error unreserving product: {e}")
            identifier = uuid if uuid else (f"section_id {section_id}" if section_id else f"reservation_pin {reservation_pin}")
            return {"success": False, "error": f"Database error while unreserving product {identifier}: {str(e)}"}

    def _handle_storage_command(self, command_name: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle storage commands"""
        logger.info(f"Storage command: {command_name}")

        if command_name == "edit_reservation":
            return self._edit_storage_reservation(payload)
        else:
            logger.error(f"Unknown storage command: {command_name}")
            return {"error": f"Unknown storage command: {command_name}"}

    def _edit_storage_reservation(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Edit storage reservation in database"""
        uuid = payload.get("uuid")
        section_id = payload.get("section_id")
        status = payload.get("status")

        if not uuid and not section_id:
            return {"success": False, "error": "uuid or section_id is required"}

        logger.info(f"Editing storage reservation uuid={uuid}, section_id={section_id}: status={status}")

        try:
            success = self._update_storage_reservation_new(uuid, section_id, status)
            if success:
                return {"success": True}
            else:
                identifier = uuid if uuid else f"section_id {section_id}"
                return {"success": False, "error": f"Storage reservation with {identifier} not found in database"}
        except Exception as e:
            logger.error(f"Error updating storage reservation: {e}")
            identifier = uuid if uuid else f"section_id {section_id}"
            return {"success": False, "error": f"Database error while updating storage reservation {identifier}: {str(e)}"}

    def _update_sale_reservation(self, uuid: str, status: int) -> bool:
        """Update sale reservation in database"""
        try:
            # Ensure database connection
            self._ensure_db_connection()
            if not self.conn:
                return False

            cursor = self.conn.cursor()

            # Update reservation
            query = """
                UPDATE sale_reservations
                SET status = %s
                WHERE uuid = %s
            """
            cursor.execute(query, (status, uuid))

            # Check if any rows were affected
            success = cursor.rowcount > 0

            cursor.close()

            # Send product change status to Jetveo
            if success:
                from infrastructure.external_apis.jetveo_client import product_change_status_async
                product_change_status_async(
                    reservation_uuid=uuid,
                    status=status
                )

            logger.info(f"Sale reservation {uuid} updated: {success}")
            return success

        except mysql.connector.Error as e:
            logger.error(f"MySQL error updating sale reservation {uuid}: {e}")
            return False
        except Exception as e:
            logger.error(f"Database error updating sale reservation {uuid}: {e}")
            return False

    def _update_storage_reservation(self, uuid: str, status: int) -> bool:
        """Update storage reservation in database"""
        try:
            # Ensure database connection
            self._ensure_db_connection()
            if not self.conn:
                return False

            cursor = self.conn.cursor()

            # Update reservation
            query = """
                UPDATE storage_reservations
                SET status = %s
                WHERE uuid = %s
            """
            cursor.execute(query, (status, uuid))

            # Check if any rows were affected
            success = cursor.rowcount > 0

            cursor.close()

            # Send storage change status to Jetveo
            storage_change_status_async(
                reservation_uuid=uuid,
                status=status
            )

            logger.info(f"Storage reservation {uuid} updated: {success}")
            return success

        except mysql.connector.Error as e:
            logger.error(f"MySQL error updating storage reservation {uuid}: {e}")
            return False
        except Exception as e:
            logger.error(f"Database error updating storage reservation {uuid}: {e}")
            return False

    def _update_sale_reservation_new(self, uuid: str, section_id: int, status: int, price: float) -> bool:
        """Update sale reservation in database with new parameters"""
        try:
            # Ensure database connection
            self._ensure_db_connection()
            if not self.conn:
                return False

            cursor = self.conn.cursor()

            # Build query based on available parameters
            if uuid:
                where_clause = "uuid = %s"
                where_param = uuid
            else:
                where_clause = "section_id = %s"
                where_param = section_id

            # First, check if the reservation exists with status = 1
            check_query = f"SELECT COUNT(*) FROM sale_reservations WHERE {where_clause} AND status = 1"
            cursor.execute(check_query, (where_param,))
            exists = cursor.fetchone()[0] > 0

            if not exists:
                # Reservation doesn't exist
                cursor.close()
                return False

            # Build SET clause for optional parameters
            set_parts = []
            set_params = []

            if status is not None:
                set_parts.append("status = %s")
                set_params.append(status)

            if price is not None:
                set_parts.append("price = %s")
                set_params.append(price)

            if not set_parts:
                # Nothing to update, but reservation exists, so return success
                cursor.close()
                return True

            # Perform the update
            query = f"UPDATE sale_reservations SET {', '.join(set_parts)} WHERE {where_clause} AND status = 1"
            cursor.execute(query, set_params + [where_param])

            # Since we already confirmed the reservation exists, return success
            # regardless of whether the update actually changed anything
            success = True

            cursor.close()

            # Send product change status to Jetveo
            if success and uuid:
                from infrastructure.external_apis.jetveo_client import product_change_status_async
                product_change_status_async(
                    reservation_uuid=uuid,
                    section_id=section_id,
                    price=price,
                    status=status
                )

            logger.info(f"Sale reservation updated: {success}")
            return success

        except mysql.connector.Error as e:
            logger.error(f"MySQL error updating sale reservation: {e}")
            return False
        except Exception as e:
            logger.error(f"Database error updating sale reservation: {e}")
            return False

    def _reserve_sale_product(self, uuid: str, section_id: int) -> int:
        """Reserve sale product and generate reservation pin"""
        from infrastructure.repositories.pin_generator import generate_pin

        try:
            # Ensure database connection
            self._ensure_db_connection()
            if not self.conn:
                return None

            cursor = self.conn.cursor()

            # Build query based on available parameters
            if uuid:
                where_clause = "uuid = %s"
                where_param = uuid
            else:
                where_clause = "section_id = %s"
                where_param = section_id

            # First, check if the reservation exists and is not already reserved
            check_query = f"SELECT reserved FROM sale_reservations WHERE {where_clause} AND status = 1"
            cursor.execute(check_query, (where_param,))
            result = cursor.fetchone()

            if not result:
                # Reservation doesn't exist
                cursor.close()
                return None

            if result[0] == 1:
                # Product is already reserved
                logger.info(f"Product is already reserved: {where_clause}={where_param}")
                cursor.close()
                return None

            # Generate unique 6-digit reservation pin
            reservation_pin = generate_pin()
            if not reservation_pin:
                logger.error("Failed to generate unique reservation pin")
                cursor.close()
                return None

            # Convert to integer for database storage
            reservation_pin = int(reservation_pin)

            # Update the reservation to reserved status
            query = f"UPDATE sale_reservations SET reserved = 1, reservation_pin = %s WHERE {where_clause} AND status = 1 AND reserved = 0"
            cursor.execute(query, (reservation_pin, where_param))

            # Check if any rows were affected
            success = cursor.rowcount > 0

            cursor.close()

            if success:
                logger.info(f"Product reserved with pin: {reservation_pin}")
                return reservation_pin
            else:
                return None

        except mysql.connector.Error as e:
            logger.error(f"MySQL error reserving product: {e}")
            return None
        except Exception as e:
            logger.error(f"Database error reserving product: {e}")
            return None

    def _unreserve_sale_product(self, uuid: str, section_id: int, reservation_pin: int) -> bool:
        """Unreserve sale product and clear reservation pin"""
        try:
            # Ensure database connection
            self._ensure_db_connection()
            if not self.conn:
                return False

            cursor = self.conn.cursor()

            # Build query based on available parameters
            if uuid:
                where_clause = "uuid = %s"
                where_param = uuid
            elif section_id:
                where_clause = "section_id = %s"
                where_param = section_id
            else:
                where_clause = "reservation_pin = %s"
                where_param = reservation_pin

            query = f"UPDATE sale_reservations SET reserved = 0, reservation_pin = NULL WHERE {where_clause} AND status = 1"
            cursor.execute(query, (where_param,))

            # Check if any rows were affected
            success = cursor.rowcount > 0

            cursor.close()

            logger.info(f"Product unreserved: {success}")
            return success

        except mysql.connector.Error as e:
            logger.error(f"MySQL error unreserving product: {e}")
            return False
        except Exception as e:
            logger.error(f"Database error unreserving product: {e}")
            return False

    def _update_storage_reservation_new(self, uuid: str, section_id: int, status: int) -> bool:
        """Update storage reservation in database with new parameters"""
        try:
            # Ensure database connection
            self._ensure_db_connection()
            if not self.conn:
                return False

            cursor = self.conn.cursor()

            # Build query based on available parameters
            if uuid:
                where_clause = "uuid = %s"
                where_param = uuid
            else:
                where_clause = "section_id = %s"
                where_param = section_id

            if status is None:
                return False  # Nothing to update

            # First, check if the reservation exists with status = 1
            check_query = f"SELECT COUNT(*) FROM storage_reservations WHERE {where_clause} AND status = 1"
            cursor.execute(check_query, (where_param,))
            exists = cursor.fetchone()[0] > 0

            if not exists:
                # Reservation doesn't exist
                cursor.close()
                return False

            # Perform the update
            query = f"UPDATE storage_reservations SET status = %s WHERE {where_clause} AND status = 1"
            cursor.execute(query, (status, where_param))

            # Send storage change status to Jetveo
            storage_change_status_async(
                reservation_uuid=uuid,
                status=status
            )

            # Since we already confirmed the reservation exists, return success
            # regardless of whether the update actually changed anything
            success = True

            cursor.close()

            logger.info(f"Storage reservation updated: {success}")
            return success

        except mysql.connector.Error as e:
            logger.error(f"MySQL error updating storage reservation: {e}")
            return False
        except Exception as e:
            logger.error(f"Database error updating storage reservation: {e}")
            return False

    def publish_response(self, command_topic: str, response: Dict[str, Any]):
        """Publish a response message to the corresponding response topic. Only works if MQTT is enabled."""
        if not self.mqtt_enabled:
            logger.debug("MQTT is disabled, skipping response publish")
            return

        if self.client and self.is_connected:
            try:
                # Convert command topic to response topic
                # devices/{clientid}/commands/{category}/{command} -> devices/{clientid}/responses/{category}/{command}
                response_topic = command_topic.replace("/commands/", "/responses/")

                self.client.publish(response_topic, json.dumps(response))
                logger.info(f"Published to {response_topic}: {response}")
            except Exception as e:
                logger.error(f"Error publishing response: {e}")
        else:
            logger.warning("Cannot publish response - client not connected")

    def publish_general_response(self, response: Dict[str, Any]):
        """Publish a response message to the general response topic. Only works if MQTT is enabled."""
        if not self.mqtt_enabled:
            logger.debug("MQTT is disabled, skipping general response publish")
            return

        if self.client and self.is_connected:
            try:
                self.client.publish(self.base_response_topic, json.dumps(response))
                logger.info(f"Published to {self.base_response_topic}: {response}")
            except Exception as e:
                logger.error(f"Error publishing response: {e}")
        else:
            logger.warning("Cannot publish response - client not connected")
    
    def add_message_handler(self, topic_pattern: str, handler: Callable):
        """Add a custom message handler for specific topics. Only works if MQTT is enabled."""
        if not self.mqtt_enabled:
            logger.debug("MQTT is disabled, skipping message handler addition")
            return
        self.message_handlers[topic_pattern] = handler

    def remove_message_handler(self, topic_pattern: str):
        """Remove a message handler. Only works if MQTT is enabled."""
        if not self.mqtt_enabled:
            logger.debug("MQTT is disabled, skipping message handler removal")
            return
        if topic_pattern in self.message_handlers:
            del self.message_handlers[topic_pattern]
    
    def start(self):
        """Start the MQTT client in a separate thread. Only starts if MQTT is enabled."""
        if not self.mqtt_enabled:
            logger.info("MQTT is disabled, client will not start")
            return

        if self.is_running:
            logger.warning("MQTT client is already running")
            return

        self.setup_client()

        if not self.broker:
            logger.error("MQTT_HOST not configured in environment")
            return

        try:
            self.client.connect(self.broker, self.port, 60)
            self.is_running = True

            # Start the network loop in a separate thread
            self.loop_thread = threading.Thread(target=self._run_loop, daemon=True)
            self.loop_thread.start()

            logger.info("MQTT client started")

        except Exception as e:
            logger.error(f"Failed to start MQTT client: {e}")
            self.is_running = False
    
    def _run_loop(self):
        """Run the MQTT network loop"""
        try:
            self.client.loop_forever()
        except Exception as e:
            logger.error(f"MQTT loop error: {e}")
        finally:
            self.is_running = False
    
    def stop(self):
        """Stop the MQTT client"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        if self.client:
            try:
                self.client.disconnect()
                self.client.loop_stop()
            except Exception as e:
                logger.error(f"Error stopping MQTT client: {e}")
        
        if self.loop_thread and self.loop_thread.is_alive():
            self.loop_thread.join(timeout=5)
            
        logger.info("MQTT client stopped")
    
    def is_client_connected(self) -> bool:
        """Check if the client is connected. Returns False if MQTT is disabled."""
        if not self.mqtt_enabled:
            return False
        return self.is_connected and self.is_running

# Global MQTT client instance
mqtt_client = MQTTClient()

def start_mqtt_client():
    """Start the global MQTT client"""
    mqtt_client.start()

def stop_mqtt_client():
    """Stop the global MQTT client"""
    mqtt_client.stop()

def get_mqtt_client() -> MQTTClient:
    """Get the global MQTT client instance"""
    return mqtt_client

