"""
Jetveo API Client - External API integration for product data.
Handles communication with Jetveo API for product information, PIN validation, and heartbeat.

Features:
1. Product Data: Fetch product information by EAN, search products, get categories
2. PIN Validation: Validate operator PINs against Jetveo server
3. Heartbeat Service: Automatic "I'm alive" messages sent periodically

PIN Check Integration:
- The check_pin() method is ready to use for PIN validation
- To switch from MockResponse to real API in box/router.py:
  1. Uncomment: response_data = await jetveo_client.check_pin(pin, SERIAL_NUMBER or "unknown")
  2. Remove the MockResponse lines
  3. Ensure EXTERNAL_API_BASE_URL and EXTERNAL_API_TOKEN are properly configured

Heartbeat Service:
- Automatically started when the application starts (see main.py lifespan)
- Only runs if EXTERNAL_API_ENABLE environment variable is set to true
- Sends POST /api/imalive every 9 minutes by default
- Configurable via JETVEO_HEARTBEAT_INTERVAL_MINUTES environment variable
- Uses SERIAL_NUMBER from environment for device identification
"""

import logging
import aiohttp
import asyncio
import mysql.connector
import json
import os
from typing import Optional, Dict, Any
from os import getenv
from dotenv import load_dotenv
import threading
from datetime import datetime, timedelta

from sale.models import ExternalProduct, ExternalProductResponse

load_dotenv()

logger = logging.getLogger(__name__)

class JetveoClient:
    """Client for Jetveo external API"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_url = getenv("EXTERNAL_API_BASE_URL", "https://4a8fa4e1-8948-4b4c-9baa-4839d158ad96.eu.jetveo.io")
        self.partner_code = getenv("EXTERNAL_API_PARTNER_CODE", "drmax")
        self.api_token = getenv("EXTERNAL_API_TOKEN", "Gj1lojJdMIrgC13psFsWwveas8PYLdUC")
        self.timeout = int(getenv("EXTERNAL_API_TIMEOUT", "10"))
        self.heartbeat_interval = int(getenv("JETVEO_HEARTBEAT_INTERVAL_MINUTES", "9")) * 60  # Convert to seconds
        self.serial_number = getenv("SERIAL_NUMBER", "unknown")
        self.external_api_enabled = getenv("EXTERNAL_API_ENABLE", "false").lower() in ("true", "1", "yes", "on")
        self.request_queue_duration = int(getenv("EXTERNAL_API_REQUEST_QUEUE_DURATION", "300"))  # 5 minutes default
        self._heartbeat_task = None
        self._heartbeat_running = False

    def _get_db_connection(self):
        """Get database connection for server request queue"""
        return mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
    
    async def fetch_product_by_ean(self, ean: str) -> Optional[ExternalProduct]:
        """
        Fetch product information from external API by EAN code.
        Only fetches if external API is enabled.

        Args:
            ean: EAN code to search for

        Returns:
            ExternalProduct object if found, None otherwise
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping product fetch")
            return None

        try:
            url = f"{self.base_url}/api/product"
            params = {
                "offset": 0,
                "limit": 100,  # Search in larger batch to increase chance of finding EAN
                "partner-code": self.partner_code
            }
            
            headers = {
                "jv-api-key": self.api_token
            }
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Parse response using Pydantic model
                        external_response = ExternalProductResponse(**data)
                        
                        if external_response.success:
                            # Search for product with matching EAN
                            for product in external_response.items:
                                if product.ean == ean:
                                    self.logger.info(f"Found product for EAN {ean}: {product.text.cs.name}")
                                    return product
                            
                            self.logger.warning(f"Product with EAN {ean} not found in external API")
                            return None
                        else:
                            self.logger.error(f"External API returned unsuccessful response: {external_response.message}")
                            return None
                    else:
                        self.logger.error(f"External API request failed with status {response.status}")
                        return None
                        
        except asyncio.TimeoutError:
            self.logger.error(f"External API request timed out for EAN {ean}")
            return None
        except Exception as e:
            self.logger.error(f"Error fetching product from external API: {e}")
            return None
    
    async def search_products(self, query: str, limit: int = 100) -> Optional[ExternalProductResponse]:
        """
        Search products in external API.
        Only searches if external API is enabled.

        Args:
            query: Search query
            limit: Maximum number of results

        Returns:
            ExternalProductResponse object or None if failed/disabled
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping product search")
            return None

        try:
            url = f"{self.base_url}/api/product"
            params = {
                "offset": 0,
                "limit": limit,
                "partner-code": self.partner_code,
                "q": query
            }
            
            headers = {
                "jv-api-key": self.api_token
            }
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return ExternalProductResponse(**data)
                    else:
                        self.logger.error(f"External API search failed with status {response.status}")
                        return None
                        
        except asyncio.TimeoutError:
            self.logger.error(f"External API search timed out for query: {query}")
            return None
        except Exception as e:
            self.logger.error(f"Error searching products in external API: {e}")
            return None
    
    async def get_product_categories(self) -> Optional[Dict[str, Any]]:
        """
        Get product categories from external API.
        Only fetches if external API is enabled.

        Returns:
            Categories data or None if failed/disabled
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping categories fetch")
            return None

        try:
            url = f"{self.base_url}/api/categories"
            params = {
                "partner-code": self.partner_code
            }
            
            headers = {
                "jv-api-key": self.api_token
            }
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        self.logger.error(f"External API categories request failed with status {response.status}")
                        return None
                        
        except asyncio.TimeoutError:
            self.logger.error("External API categories request timed out")
            return None
        except Exception as e:
            self.logger.error(f"Error fetching categories from external API: {e}")
            return None
    
    async def check_pin(self, pin: str, serial_number: str) -> Optional[Dict[str, Any]]:
        """
        Check PIN validity with external API.
        Only checks if external API is enabled.

        Args:
            pin: PIN code to validate
            serial_number: Device serial number

        Returns:
            Dict with PIN check result or None if failed/disabled
            Expected response format:
            {
                "serial_number": "xxxx",
                "status": "allow" | "deny",
                "operator_id": int,
                "name": "Operator Name",
                "type": "1" | "2" | etc.
            }
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping PIN check")
            return None

        try:
            url = f"{self.base_url}/pin_check"

            # Prepare request data
            request_data = {
                "serial_number": serial_number,
                "scanned_pin": pin
            }

            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }

            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.logger.info(f"PIN check successful for PIN {pin}")
                        return data
                    else:
                        self.logger.error(f"PIN check failed with status {response.status}")
                        return None

        except asyncio.TimeoutError:
            self.logger.error(f"PIN check request timed out for PIN {pin}")
            return None
        except Exception as e:
            self.logger.error(f"Error checking PIN with external API: {e}")
            return None

    async def send_heartbeat(self) -> bool:
        """
        Send "I'm alive" heartbeat to Jetveo server.
        Only sends if external API is enabled.

        Returns:
            True if heartbeat sent successfully, False otherwise
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping heartbeat")
            return False

        try:
            url = f"{self.base_url}/api/imalive"

            request_data = {
                "SerialNumber": self.serial_number
            }

            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }

            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        self.logger.info(f"Heartbeat sent successfully for device {self.serial_number}")
                        return True
                    else:
                        self.logger.warning(f"Heartbeat failed with status {response.status}")
                        return False

        except asyncio.TimeoutError:
            self.logger.error("Heartbeat request timed out")
            return False
        except Exception as e:
            self.logger.error(f"Error sending heartbeat: {e}")
            return False

    async def _heartbeat_loop(self):
        """
        Internal heartbeat loop that runs periodically.
        Also retries failed requests from the queue.
        """
        self.logger.info(f"Starting heartbeat loop with interval {self.heartbeat_interval} seconds")

        while self._heartbeat_running:
            try:
                # Send heartbeat
                await self.send_heartbeat()

                # Retry failed requests
                try:
                    retried_count = await self.retry_failed_requests()
                    if retried_count > 0:
                        self.logger.info(f"Retried {retried_count} failed requests during heartbeat cycle")
                except Exception as retry_error:
                    self.logger.error(f"Error retrying failed requests: {retry_error}")

                await asyncio.sleep(self.heartbeat_interval)
            except asyncio.CancelledError:
                self.logger.info("Heartbeat loop cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in heartbeat loop: {e}")
                # Wait a bit before retrying
                await asyncio.sleep(60)

    def start_heartbeat(self):
        """
        Start the periodic heartbeat in the background.
        Only starts if external API is enabled.
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, heartbeat will not start")
            return

        if self._heartbeat_running:
            self.logger.warning("Heartbeat is already running")
            return

        self._heartbeat_running = True

        # Create new event loop for the heartbeat if we're not in an async context
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # We're in an async context, create task
                self._heartbeat_task = loop.create_task(self._heartbeat_loop())
            else:
                # Start in a separate thread
                def run_heartbeat():
                    asyncio.run(self._heartbeat_loop())

                heartbeat_thread = threading.Thread(target=run_heartbeat, daemon=True)
                heartbeat_thread.start()

        except RuntimeError:
            # No event loop, start in a separate thread
            def run_heartbeat():
                asyncio.run(self._heartbeat_loop())

            heartbeat_thread = threading.Thread(target=run_heartbeat, daemon=True)
            heartbeat_thread.start()

        self.logger.info("Heartbeat started")

    def stop_heartbeat(self):
        """
        Stop the periodic heartbeat.
        """
        if not self._heartbeat_running:
            return

        self._heartbeat_running = False

        if self._heartbeat_task and not self._heartbeat_task.done():
            self._heartbeat_task.cancel()

        self.logger.info("Heartbeat stopped")

    async def check_employment_send(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """
        Check if phone number is valid for employment send operation.

        Args:
            phone_number: Employee's phone number

        Returns:
            Dict with validation result or None if failed
            Expected response format:
            {
                "phone_number": "123456789",
                "valid": true,
                "section_id": null/1
            }
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, using mock response for employment send check")
            # Mock responses for testing
            if phone_number == "123456789":
                return {
                    "phone_number": phone_number,
                    "valid": True,
                    "section_id": 5  # Pre-reserved section
                }
            elif phone_number == "987654321":
                return {
                    "phone_number": phone_number,
                    "valid": True,
                    "section_id": None  # No pre-reserved section
                }
            else:
                return {
                    "phone_number": phone_number,
                    "valid": False,
                    "section_id": None
                }

        try:
            url = f"{self.base_url}/api/employment/send"

            request_data = {
                "phone_number": phone_number
            }

            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }

            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.logger.info(f"Employment send check successful for phone {phone_number}")
                        return data
                    else:
                        self.logger.error(f"Employment send check failed with status {response.status}")
                        return None

        except asyncio.TimeoutError:
            self.logger.error(f"Employment send check request timed out for phone {phone_number}")
            return None
        except Exception as e:
            self.logger.error(f"Error checking employment send with external API: {e}")
            return None

    async def check_employment_deliver(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """
        Check if phone number is valid for employment deliver operation.

        Args:
            phone_number: Employee's phone number

        Returns:
            Dict with validation result or None if failed
            Expected response format:
            {
                "phone_number": "123456789",
                "valid": true,
                "section_ids": null/[1,2,3]
            }
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, using mock response for employment deliver check")
            # Mock responses for testing
            if phone_number == "123456789":
                return {
                    "phone_number": phone_number,
                    "valid": True,
                    "section_ids": [1, 2, 3]  # Pre-reserved sections for delivery
                }
            elif phone_number == "555666777":
                return {
                    "phone_number": phone_number,
                    "valid": True,
                    "section_ids": None  # No pre-reserved sections
                }
            else:
                return {
                    "phone_number": phone_number,
                    "valid": False,
                    "section_ids": None
                }

        try:
            url = f"{self.base_url}/api/employment/deliver"

            request_data = {
                "phone_number": phone_number
            }

            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }

            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.logger.info(f"Employment deliver check successful for phone {phone_number}")
                        return data
                    else:
                        self.logger.error(f"Employment deliver check failed with status {response.status}")
                        return None

        except asyncio.TimeoutError:
            self.logger.error(f"Employment deliver check request timed out for phone {phone_number}")
            return None
        except Exception as e:
            self.logger.error(f"Error checking employment deliver with external API: {e}")
            return None

    async def check_employment_reclaim(self, reclamation_pin: str) -> Optional[Dict[str, Any]]:
        """
        Check if reclamation PIN is valid for employment reclaim operation.

        Args:
            reclamation_pin: Reclamation PIN to validate

        Returns:
            Dict with validation result or None if failed
            Expected response format:
            {
                "phone_number": "123456789",
                "valid": true,
                "section_id": null/1
            }
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, using mock response for employment reclaim check")
            # Mock responses for testing
            if reclamation_pin == "RECLAIM123":
                return {
                    "phone_number": "123456789",
                    "valid": True,
                    "section_id": 7  # Pre-reserved section for reclaim
                }
            elif reclamation_pin == "RECLAIM456":
                return {
                    "phone_number": "987654321",
                    "valid": True,
                    "section_id": None  # No pre-reserved section
                }
            else:
                return {
                    "phone_number": None,
                    "valid": False,
                    "section_id": None
                }

        try:
            url = f"{self.base_url}/api/employment/reclaim"

            request_data = {
                "reservation_pin": reclamation_pin
            }

            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }

            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.logger.info(f"Employment reclaim check successful for PIN {reclamation_pin}")
                        return data
                    else:
                        self.logger.error(f"Employment reclaim check failed with status {response.status}")
                        return None

        except asyncio.TimeoutError:
            self.logger.error(f"Employment reclaim check request timed out for PIN {reclamation_pin}")
            return None
        except Exception as e:
            self.logger.error(f"Error checking employment reclaim with external API: {e}")
            return None

    async def test_connection(self) -> bool:
        """
        Test connection to external API.
        Only tests if external API is enabled.

        Returns:
            True if connection successful, False if failed/disabled
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping connection test")
            return False

        try:
            url = f"{self.base_url}/api/product"
            params = {
                "offset": 0,
                "limit": 1,
                "partner-code": self.partner_code
            }

            headers = {
                "jv-api-key": self.api_token
            }

            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    return response.status == 200

        except Exception as e:
            self.logger.error(f"External API connection test failed: {e}")
            return False

    async def get_layout(self, serial_number: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get layout data from external API or return mock data.
        If external API is disabled, returns mock response from JSON file.

        Args:
            serial_number: Serial number to use for the request. If None, uses self.serial_number

        Returns:
            Dict with layout data if successful, None otherwise
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, returning mock layout data")
            return self._get_mock_layout_response()

        try:
            url = f"{self.base_url}/api/get-layout"

            # Use provided serial number or fall back to instance serial number
            request_serial = serial_number or self.serial_number

            request_data = {
                "SerialNumber": request_serial
            }

            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }

            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.logger.info(f"Layout data fetched successfully for serial: {request_serial}")
                        return data
                    else:
                        self.logger.error(f"Layout fetch failed with status {response.status} for serial: {request_serial}")
                        return None

        except asyncio.TimeoutError:
            self.logger.error(f"Layout fetch request timed out for serial: {request_serial}")
            return None
        except Exception as e:
            self.logger.error(f"Error fetching layout data for serial {request_serial}: {e}")
            return None

    def _get_mock_layout_response(self) -> Optional[Dict[str, Any]]:
        """
        Load and return mock layout response from JSON file.

        Returns:
            Dict with mock layout data if file exists and is valid, None otherwise
        """
        try:
            # Get the directory where this file is located
            current_dir = os.path.dirname(os.path.abspath(__file__))
            mock_file_path = os.path.join(current_dir, "mock_layout_response.json")

            if not os.path.exists(mock_file_path):
                self.logger.error(f"Mock layout response file not found: {mock_file_path}")
                return None

            with open(mock_file_path, 'r', encoding='utf-8') as f:
                mock_data = json.load(f)

            self.logger.info("Mock layout data loaded successfully")
            return mock_data

        except json.JSONDecodeError as e:
            self.logger.error(f"Error parsing mock layout JSON file: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error loading mock layout response: {e}")
            return None

    async def send_to_journal(
        self,
        serial_number: str,
        timestamp: str,
        entered_pin: Optional[str] = None,
        event_result: Optional[str] = None,
        event_type: Optional[str] = None,
        message: Optional[str] = None,
        operator_id: Optional[str] = None,
        order_number: Optional[str] = None,
        section_id: Optional[str] = None,
        session_id: Optional[str] = None,
        tempered_unlock: Optional[str] = None,
        type: Optional[str] = None
    ) -> bool:
        """
        Send journal data to Jetveo API /api/journal endpoint.
        Only sends if external API is enabled. If request fails, saves to server_request_queue.

        Args:
            serial_number: Device serial number (required)
            timestamp: Event timestamp in ISO format (required)
            entered_pin: PIN that was entered
            event_result: Result of the event
            event_type: Type of event
            message: Event message
            operator_id: ID of the operator
            order_number: Order number
            section_id: Section ID
            session_id: Session ID
            tempered_unlock: Tampered unlock status
            type: Event type

        Returns:
            True if journal data sent successfully, False otherwise
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping journal data send")
            return True

        request_data = {
            "SerialNumber": serial_number,
            "Timestamp": timestamp,
            "EnteredPin": entered_pin or "",
            "EventResult": event_result or "",
            "EventType": event_type or "",
            "Message": message or "",
            "OperatorId": operator_id or "",
            "OrderNumber": order_number or "",
            "SectionId": section_id or "",
            "SessionId": session_id or "",
            "TemperedUnlock": tempered_unlock or "",
            "Type": type or ""
        }

        try:
            url = f"{self.base_url}/api/journal"
            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        # Check if response contains "success": true
                        if response_data.get("success") is True:
                            self.logger.info(f"Journal data sent successfully for device {serial_number}")
                            return True
                        else:
                            self.logger.warning(f"Journal data send failed - server returned success: {response_data.get('success')}")
                            self._save_failed_request("/api/journal", "POST", request_data)
                            return False
                    else:
                        self.logger.warning(f"Journal data send failed with status {response.status}")
                        self._save_failed_request("/api/journal", "POST", request_data)
                        return False

        except asyncio.TimeoutError:
            self.logger.error("Journal data send request timed out")
            self._save_failed_request("/api/journal", "POST", request_data)
            return False
        except Exception as e:
            self.logger.error(f"Error sending journal data: {e}")
            self._save_failed_request("/api/journal", "POST", request_data)
            return False

    def _save_failed_request(self, endpoint: str, action: str, payload: Dict[str, Any]):
        """
        Save failed request to server_request_queue table.
        Saves /api/journal and /api/product-change-status requests, not /api/imalive.
        """
        if endpoint not in ["/api/journal", "/api/product-change-status"]:
            return

        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()

            query = """
                INSERT INTO server_request_queue (
                    device_status, enpoint, action, error, payload, sent_attemps
                ) VALUES (%s, %s, %s, %s, %s, %s)
            """
            values = (
                1,  # device_status = 1 (active)
                endpoint,
                action,
                0,  # error = 0 (not successfully sent yet)
                json.dumps(payload),
                0   # sent_attemps = 0 (initial)
            )

            cursor.execute(query, values)
            conn.commit()
            self.logger.info(f"Failed request saved to queue: {endpoint}")

        except mysql.connector.Error as err:
            self.logger.error(f"Database error saving failed request: {err}")
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    async def server_ping(self) -> bool:
        """
        Check if server is available using /api/imalive endpoint.

        Returns:
            True if server is available and returns "success": true, False otherwise
        """
        if not self.external_api_enabled:
            return False

        try:
            url = f"{self.base_url}/api/imalive"
            request_data = {
                "SerialNumber": self.serial_number
            }
            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        return response_data.get("success") is True
                    else:
                        return False

        except Exception as e:
            self.logger.debug(f"Server ping failed: {e}")
            return False

    async def retry_failed_requests(self) -> int:
        """
        Retry failed requests from server_request_queue.
        Only retries if required time has passed and server is available.

        Returns:
            Number of requests successfully retried
        """
        if not self.external_api_enabled:
            return 0

        # Check if server is available
        if not await self.server_ping():
            self.logger.debug("Server ping failed, skipping retry of failed requests")
            return 0

        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()

            # Get failed requests that are ready to retry
            cutoff_time = datetime.now() - timedelta(seconds=self.request_queue_duration)

            query = """
                SELECT id, enpoint, action, payload, sent_attemps
                FROM server_request_queue
                WHERE error = 0 AND sent_attemps < 5 AND created_at <= %s
                ORDER BY created_at ASC
                LIMIT 10
            """
            cursor.execute(query, (cutoff_time,))
            failed_requests = cursor.fetchall()

            successful_retries = 0

            for request_id, endpoint, action, payload_json, sent_attempts in failed_requests:
                try:
                    payload = json.loads(payload_json)

                    # Retry based on endpoint type
                    if endpoint == "/api/journal":
                        success = await self._retry_journal_request(payload)
                    elif endpoint == "/api/product-change-status":
                        success = await self._retry_product_change_status_request(payload)
                    else:
                        success = False

                        if success:
                            # Mark as successful (error = 1 means successfully sent)
                            update_query = "UPDATE server_request_queue SET error = 1, sent_attemps = %s WHERE id = %s"
                            cursor.execute(update_query, (sent_attempts + 1, request_id))
                            successful_retries += 1
                        else:
                            # Increment sent_attempts
                            new_attempts = sent_attempts + 1
                            if new_attempts >= 5:
                                # Mark as error after 5 attempts (error = 1)
                                update_query = "UPDATE server_request_queue SET error = 1, sent_attemps = %s WHERE id = %s"
                                cursor.execute(update_query, (new_attempts, request_id))
                                self.logger.warning(f"Request {request_id} marked as failed after 5 attempts")
                            else:
                                # Just increment attempts
                                update_query = "UPDATE server_request_queue SET sent_attemps = %s WHERE id = %s"
                                cursor.execute(update_query, (new_attempts, request_id))

                except json.JSONDecodeError:
                    self.logger.error(f"Invalid JSON payload in request {request_id}")
                    # Mark as error
                    update_query = "UPDATE server_request_queue SET error = 1 WHERE id = %s"
                    cursor.execute(update_query, (request_id,))

            conn.commit()

            if successful_retries > 0:
                self.logger.info(f"Successfully retried {successful_retries} failed requests")

            return successful_retries

        except mysql.connector.Error as err:
            self.logger.error(f"Database error during retry: {err}")
            return 0
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    async def _retry_journal_request(self, payload: Dict[str, Any]) -> bool:
        """
        Retry a journal request with the given payload.

        Returns:
            True if successful, False otherwise
        """
        try:
            url = f"{self.base_url}/api/journal"
            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        return response_data.get("success") is True
                    else:
                        return False

        except Exception as e:
            self.logger.debug(f"Retry journal request failed: {e}")
            return False

    async def _retry_product_change_status_request(self, payload: Dict[str, Any]) -> bool:
        """
        Retry a product change status request with the given payload.

        Returns:
            True if successful, False otherwise
        """
        try:
            url = f"{self.base_url}/api/product-change-status"
            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        return response_data.get("success", True)
                    else:
                        return False

        except Exception as e:
            self.logger.debug(f"Retry product change status request failed: {e}")
            return False

    async def storage_change_status(
        self,
        reservation_uuid: str,
        serial_number: Optional[str] = None,
        reservation_pin: Optional[str] = None,
        section_id: Optional[int] = None,
        email: Optional[str] = None,
        size_category: Optional[int] = None,
        paid_price: Optional[float] = None,
        paid_fine: Optional[float] = None,
        timestamp: Optional[str] = None,
        action: Optional[int] = None,
        status: Optional[int] = None
    ) -> bool:
        """
        Send storage change status data to Jetveo API /api/product-change-status endpoint.
        Only sends if external API is enabled.

        Args:
            reservation_uuid: Reservation UUID (required)
            serial_number: Device serial number (defaults to env SERIAL_NUMBER)
            reservation_pin: Reservation PIN (optional)
            section_id: Section ID (optional)
            email: Customer email (optional)
            size_category: Size category (optional)
            paid_price: Paid price amount (optional)
            paid_fine: Paid fine amount (optional)
            timestamp: Timestamp in ISO format (optional)
            action: Action code (optional)
            status: Status code (optional)

        Returns:
            True if successful, False otherwise
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping storage change status")
            return False

        # Use default serial number if not provided
        if serial_number is None:
            serial_number = self.serial_number

        # Use current timestamp if not provided
        if timestamp is None:
            from datetime import datetime, timezone
            timestamp = datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')

        request_data = {
            "SerialNumber": serial_number,
            "ReservationUuid": reservation_uuid,
            "ReservationPin": reservation_pin or "",
            "SectionId": section_id or 0,
            "Email": email or "",
            "SizeCategory": size_category or 0,
            "PaidPrice": paid_price or 0,
            "PaidFine": paid_fine or 0,
            "Timestamp": timestamp,
            "Action": action or 0,
            "Status": status or 0
        }

        try:
            url = f"{self.base_url}/api/product-change-status"
            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        self.logger.info(f"Storage change status sent successfully for section {section_id}")
                        return response_data.get("success", True)
                    else:
                        self.logger.error(f"Storage change status failed with status {response.status}")
                        self._save_failed_request("/api/product-change-status", "storage_change_status", request_data)
                        return False

        except asyncio.TimeoutError:
            self.logger.error("Storage change status request timed out")
            self._save_failed_request("/api/product-change-status", "storage_change_status", request_data)
            return False
        except Exception as e:
            self.logger.error(f"Error sending storage change status: {e}")
            self._save_failed_request("/api/product-change-status", "storage_change_status", request_data)
            return False

    async def product_change_status(
        self,
        reservation_uuid: str,
        serial_number: Optional[str] = None,
        reserved: Optional[bool] = None,
        reservation_pin: Optional[str] = None,
        section_id: Optional[int] = None,
        timestamp: Optional[str] = None,
        price: Optional[float] = None,
        ean: Optional[str] = None,
        action: Optional[int] = None,
        status: Optional[int] = None
    ) -> bool:
        """
        Send product change status data to Jetveo API /api/product-change-status endpoint.
        Only sends if external API is enabled.

        Args:
            reservation_uuid: Reservation UUID (required)
            serial_number: Device serial number (defaults to env SERIAL_NUMBER)
            reserved: Whether the product is reserved (optional)
            reservation_pin: Reservation PIN (optional)
            section_id: Section ID (optional)
            timestamp: Timestamp in ISO format (optional)
            price: Product price (optional)
            ean: Product EAN code (optional)
            action: Action code (optional)
            status: Status code (optional)

        Returns:
            True if successful, False otherwise
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping product change status")
            return False

        # Use default serial number if not provided
        if serial_number is None:
            serial_number = self.serial_number

        # Use current timestamp if not provided
        if timestamp is None:
            from datetime import datetime, timezone
            timestamp = datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')

        request_data = {
            "SerialNumber": serial_number,
            "ReservationUuid": reservation_uuid,
            "Reserved": reserved if reserved is not None else False,
            "ReservationPin": reservation_pin or "",
            "SectionId": section_id or 0,
            "Timestamp": timestamp,
            "Price": price or 0,
            "Ean": ean or "",
            "Action": action or 0,
            "Status": status or 0
        }

        try:
            url = f"{self.base_url}/api/product-change-status"
            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        self.logger.info(f"Product change status sent successfully for EAN {ean}")
                        return response_data.get("success", True)
                    else:
                        self.logger.error(f"Product change status failed with status {response.status}")
                        self._save_failed_request("/api/product-change-status", "product_change_status", request_data)
                        return False

        except asyncio.TimeoutError:
            self.logger.error("Product change status request timed out")
            self._save_failed_request("/api/product-change-status", "product_change_status", request_data)
            return False
        except Exception as e:
            self.logger.error(f"Error sending product change status: {e}")
            self._save_failed_request("/api/product-change-status", "product_change_status", request_data)
            return False
        
    

# Global client instance
jetveo_client = JetveoClient()

def storage_change_status_async(
    reservation_uuid: str,
    serial_number: Optional[str] = None,
    reservation_pin: Optional[str] = None,
    section_id: Optional[int] = None,
    email: Optional[str] = None,
    size_category: Optional[int] = None,
    paid_price: Optional[float] = None,
    paid_fine: Optional[float] = None,
    timestamp: Optional[str] = None,
    action: Optional[int] = None,
    status: Optional[int] = None
):
    """
    Synchronous wrapper for storage_change_status that runs the async call in background.
    Similar to how log_timeline_event() works - call it and it handles the async part.

    Args:
        reservation_uuid: Reservation UUID (required)
        serial_number: Device serial number (defaults to env SERIAL_NUMBER)
        reservation_pin: Reservation PIN (optional)
        section_id: Section ID (optional)
        email: Customer email (optional)
        size_category: Size category (optional)
        paid_price: Paid price amount (optional)
        paid_fine: Paid fine amount (optional)
        timestamp: Timestamp in ISO format (optional, defaults to current time)
        action: Action code (optional)
        status: Status code (optional)
    """
    from os import getenv

    # Check if external API is enabled
    external_api_enabled = getenv("EXTERNAL_API_ENABLE", "false").lower() in ("true", "1", "yes", "on")
    if not external_api_enabled:
        return

    import threading
    import asyncio

    def run_async_call():
        """Run the async call in a separate thread with its own event loop"""
        try:
            # Create a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Run the async call
            loop.run_until_complete(jetveo_client.storage_change_status(
                reservation_uuid=reservation_uuid,
                serial_number=serial_number,
                reservation_pin=reservation_pin,
                section_id=section_id,
                email=email,
                size_category=size_category,
                paid_price=paid_price,
                paid_fine=paid_fine,
                timestamp=timestamp,
                action=action,
                status=status
            ))

            loop.close()

        except Exception as e:
            # Log error but don't fail the calling operation
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in storage_change_status_async: {e}")

    # Start the async call in a separate daemon thread so it doesn't block
    thread = threading.Thread(target=run_async_call, daemon=True)
    thread.start()

def product_change_status_async(
    reservation_uuid: str,
    serial_number: Optional[str] = None,
    reserved: Optional[bool] = None,
    reservation_pin: Optional[str] = None,
    section_id: Optional[int] = None,
    timestamp: Optional[str] = None,
    price: Optional[float] = None,
    ean: Optional[str] = None,
    action: Optional[int] = None,
    status: Optional[int] = None
):
    """
    Synchronous wrapper for product_change_status that runs the async call in background.
    Similar to how log_timeline_event() works - call it and it handles the async part.

    Args:
        reservation_uuid: Reservation UUID (required)
        serial_number: Device serial number (defaults to env SERIAL_NUMBER)
        reserved: Whether the product is reserved (optional)
        reservation_pin: Reservation PIN (optional)
        section_id: Section ID (optional)
        timestamp: Timestamp in ISO format (optional, defaults to current time)
        price: Product price (optional)
        ean: Product EAN code (optional)
        action: Action code (optional)
        status: Status code (optional)
    """
    from os import getenv

    # Check if external API is enabled
    external_api_enabled = getenv("EXTERNAL_API_ENABLE", "false").lower() in ("true", "1", "yes", "on")
    if not external_api_enabled:
        return

    import threading
    import asyncio

    def run_async_call():
        """Run the async call in a separate thread with its own event loop"""
        try:
            # Create a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Run the async call
            loop.run_until_complete(jetveo_client.product_change_status(
                reservation_uuid=reservation_uuid,
                serial_number=serial_number,
                reserved=reserved,
                reservation_pin=reservation_pin,
                section_id=section_id,
                timestamp=timestamp,
                price=price,
                ean=ean,
                action=action,
                status=status
            ))

            loop.close()

        except Exception as e:
            # Log error but don't fail the calling operation
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in product_change_status_async: {e}")

    # Start the async call in a separate daemon thread so it doesn't block
    thread = threading.Thread(target=run_async_call, daemon=True)
    thread.start()

def start_jetveo_heartbeat():
    """
    Start the Jetveo heartbeat service.
    Call this function during application startup.
    """
    jetveo_client.start_heartbeat()

def stop_jetveo_heartbeat():
    """
    Stop the Jetveo heartbeat service.
    Call this function during application shutdown.
    """
    jetveo_client.stop_heartbeat()
