"""
Simple Storage Flow Engine.
Builds workflow steps for storage operations.
"""

import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class FlowEngine:
    """Simple flow engine for step-by-step execution"""
    
    def __init__(self):
        self.steps: List[Dict[str, Any]] = []
        self.current_step_index = 0
        self.completed_steps: List[str] = []
        
    def build_flow(self, flow_config: Dict[str, Any]):
        """Build flow steps from configuration"""
        logger.info(f"Building storage flow from config: {flow_config}")
        
        self.steps = []
        
        # Add payment step if required
        if flow_config.get("requires_payment", False):
            self.steps.append({
                "type": "payment", 
                "name": "Platba",
                "context": {
                    "amount": flow_config.get("amount", 0),
                    "section_id": flow_config.get("section_id"),
                    "size_category": flow_config.get("size_category")
                }
            })
            
        # Add hardware step for storage insertion
        # Note: pickup now uses pickup_process directly, not flow-based approach
        self.steps.append({
            "type": "hardware",
            "name": "Otevření schránky pro uložení",
            "context": {
                "section_id": flow_config.get("section_id"),
                "operation": "open_for_insert"
            }
        })
        
        logger.info(f"Built flow with {len(self.steps)} steps: {[s['type'] for s in self.steps]}")
    
    def get_current_step(self) -> Dict[str, Any] | None:
        """Get current step"""
        if self.current_step_index < len(self.steps):
            return self.steps[self.current_step_index]
        return None
    
    def complete_current_step(self) -> bool:
        """Mark current step as completed"""
        current_step = self.get_current_step()
        if current_step:
            self.completed_steps.append(current_step["type"])
            logger.info(f"Completed step '{current_step['type']}'")
            return True
        return False
    
    def move_to_next_step(self) -> bool:
        """Move to next step"""
        if self.current_step_index < len(self.steps):
            self.current_step_index += 1
            if self.current_step_index < len(self.steps):
                next_step = self.get_current_step()
                logger.info(f"Moved to step {self.current_step_index}: {next_step['type'] if next_step else 'None'}")
                return True
        return False
