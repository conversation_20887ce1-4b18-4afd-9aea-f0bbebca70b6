import mysql.connector
from os import getenv
from typing import List, Dict, Any
from managers.timeline_logger import log_timeline_event
from infrastructure.external_apis.jetveo_client import storage_change_status_async

def get_db():
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )

class StorageRepository:
    def get_all_categories(self) -> List[Dict[str, Any]]:
        db = get_db()
        cursor = db.cursor(dictionary=True)
        cursor.execute("SELECT size_category, price FROM storage_categories")
        categories = cursor.fetchall()
        cursor.close()
        db.close()
        return categories

    def get_category_by_section(self, section_id: int) -> Dict[str, Any]:
        db = get_db()
        cursor = db.cursor(dictionary=True)
        query = """
            SELECT sc.size_category, sc.price
            FROM storage_categories sc
            JOIN box_sections bs ON sc.size_category = bs.size_category
            WHERE bs.section_id = %s
        """
        cursor.execute(query, (section_id,))
        category = cursor.fetchone()
        cursor.close()
        db.close()
        return category

    def check_section_availability(self, section_id: int) -> dict:
        """Check if section is available for storage reservation"""
        db = get_db()
        cursor = db.cursor(dictionary=True)

        try:
            # Check if section exists and has mode == "storage"
            cursor.execute("""
                SELECT section_id, mode FROM box_sections
                WHERE section_id = %s
            """, (str(section_id),))
            section = cursor.fetchone()

            if not section:
                return {"available": False, "reason": "Section not found"}

            if section['mode'] != 'storage':
                return {"available": False, "reason": f"Section mode is '{section['mode']}', only 'storage' mode allowed"}

            # Check if there's already an active reservation for this section
            cursor.execute("""
                SELECT id FROM storage_reservations
                WHERE section_id = %s AND status = 1
            """, (str(section_id),))
            active_reservation = cursor.fetchone()

            if active_reservation:
                return {"available": False, "reason": "Section already has an active reservation"}

            return {"available": True, "reason": "Section is available"}

        except Exception as e:
            return {"available": False, "reason": f"Database error: {str(e)}"}
        finally:
            cursor.close()
            db.close()

    def create_reservation(self, section_id: int, price: float, size_category: int, email: str = None) -> str:
        import random
        from uuid import uuid4
        from os import getenv

        db = get_db()
        cursor = db.cursor()

        # Generate a unique 6-digit PIN
        from .pin_generator import generate_pin
        pin = generate_pin()

        if pin is None:
            cursor.close()
            db.close()
            raise Exception("Failed to generate unique PIN for reservation")

        # Generate UUID for the reservation
        reservation_uuid = str(uuid4())

        # Get box_uuid from box_sections table
        cursor.execute("SELECT box_uuid FROM box_sections WHERE section_id = %s", (str(section_id),))
        box_result = cursor.fetchone()
        box_uuid = box_result[0] if box_result else None

        query = """
            INSERT INTO storage_reservations (uuid, email, box_uuid, section_id, price, category, status, reservation_pin, paid_status, created_at, last_update)
            VALUES (%s, %s, %s, %s, %s, %s, 1, %s, 'paid', NOW(), NOW())
        """
        try:
            cursor.execute(query, (reservation_uuid, email, box_uuid, str(section_id), price, size_category, pin))
            db.commit()
            log_timeline_event(
                event_type="create_reservaion",
                event_result="success",
                section_id=str(section_id),
                message=f"Reservation created with PIN: {pin}",
                mode="storage"
            )

            # Send storage change status to Jetveo
            storage_change_status_async(
                reservation_uuid=reservation_uuid,
                reservation_pin=pin,
                section_id=section_id,
                email=email or "",
                size_category=size_category,
                paid_price=price,
                paid_fine=0,
                action=1,
                status=1
            )

            return pin
        except mysql.connector.Error as err:
            log_timeline_event(
                event_type="create_reservation",
                event_result="failed",
                section_id=str(section_id),
                message=f"Database error",
                mode="storage"
            )
            raise err
        finally:
            cursor.close()
            db.close()

    def find_reservation_by_pin(self, pin: str) -> Dict[str, Any]:
        db = get_db()
        cursor = db.cursor(dictionary=True)
        query = "SELECT * FROM storage_reservations WHERE reservation_pin = %s AND status = 1"
        cursor.execute(query, (pin,))
        reservation = cursor.fetchone()
        cursor.close()
        db.close()

        # if not reservation:
        #     log_timeline_event(
        #         event_type="box_not_found",
        #         event_result="failure",
        #         message="Storage reserved Box not found",
        #         entered_pin=pin
        #     )
        return reservation

    def get_storage_sections(self) -> List[Dict[str, Any]]:
        """Get all storage sections with availability status"""
        db = get_db()
        cursor = db.cursor(dictionary=True)

        try:
            # Get all sections with mode = 'storage'
            cursor.execute("""
                SELECT section_id, identification_name, tempered, blocked, service,
                       mode, type, size_width, size_depth, size_height, size_category
                FROM box_sections
                WHERE visible = 1 AND mode = 'storage'
                ORDER BY section_id ASC
            """)
            sections = cursor.fetchall()

            # Check availability for each section
            for section in sections:
                section_id = section['section_id']
                identification_name = section['identification_name']
                section['is_available'] = self._check_section_availability_simple(cursor, section_id, identification_name)

            return sections

        except Exception as e:
            return []
        finally:
            cursor.close()
            db.close()

    def _check_section_availability_simple(self, cursor, section_id: int, identification_name: str = None) -> bool:
        """Check if section is available (no active reservations)"""
        try:
            # Sections with identification_name == "stock" are always available
            if identification_name == "stock":
                return True

            # Check order_reservations for active reservations (status = 1)
            cursor.execute("""
                SELECT COUNT(*) as count FROM order_reservations
                WHERE section_id = %s AND status = 1
            """, (str(section_id),))
            order_count = cursor.fetchone()['count']

            if order_count > 0:
                return False

            # Check sale_reservations for active reservations (status = 1)
            cursor.execute("""
                SELECT COUNT(*) as count FROM sale_reservations
                WHERE section_id = %s AND status = 1
            """, (str(section_id),))
            sale_count = cursor.fetchone()['count']

            if sale_count > 0:
                return False

            # Check storage_reservations for active reservations (status = 1)
            cursor.execute("""
                SELECT COUNT(*) as count FROM storage_reservations
                WHERE section_id = %s AND status = 1
            """, (str(section_id),))
            storage_count = cursor.fetchone()['count']

            return storage_count == 0

        except Exception as e:
            return False

    def deactivate_reservation(self, reservation_id: int):
        """Deactivate a reservation and free up the box (called during pickup) - sets status = 0, keeps paid_status = 'paid'"""
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # First get the reservation details
            cursor.execute("""
                SELECT section_id FROM storage_reservations
                WHERE id = %s
            """, (reservation_id,))
            reservation = cursor.fetchone()

            if reservation:
                # Update reservation status to 0 (picked up/completed) but keep paid_status = 'paid'
                cursor.execute("""
                    UPDATE storage_reservations
                    SET status = 0, last_update = NOW()
                    WHERE id = %s
                """, (reservation_id,))

                # Send storage change status to Jetveo
                storage_change_status_async(
                    reservation_uuid=reservation['uuid'],
                    section_id=reservation['section_id'],
                    status=0
                )


                db.commit()
                log_timeline_event(
                    event_type="reservation_status_changed",
                    event_result="complete",
                    section_id=str(reservation['section_id']),
                    message=f"Reservation status changed to 0 for reservation ID: {reservation_id}"
                )
                return True
            return False
        except Exception as e:
            db.rollback()
            log_timeline_event(
                event_type="reservation_status_changed",
                event_result="failed",
                section_id=str(reservation['section_id']),
                message=f"Error deactivating reservation for reservation ID: {reservation_id} - {e}",
            )
            print(f"Error deactivating reservation: {e}")
            return False
        finally:
            cursor.close()
            db.close()

    def complete_reservation(self, reservation_pin: str) -> Dict[str, Any]:
        """Mark a reservation as storage completed (luggage stored) - keeps status = 1 and paid_status = 'paid'"""
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # First get the reservation details
            cursor.execute("""
                SELECT uuid, section_id FROM storage_reservations
                WHERE reservation_pin = %s AND status = 1
            """, (reservation_pin,))
            reservation = cursor.fetchone()

            if not reservation:
                return {"success": False, "error": "Reservation not found"}

            # Update last_update but keep status = 1 and paid_status = 'paid' (still active until pickup)
            cursor.execute("""
                UPDATE storage_reservations
                SET last_update = NOW()
                WHERE reservation_pin = %s AND status = 1
            """, (reservation_pin,))

            # Send storage change status to Jetveo
            storage_change_status_async(
                reservation_uuid=reservation['uuid'],
                section_id=reservation['section_id'],
                status=1
            )

            if cursor.rowcount > 0:
                db.commit()
                return {
                    "success": True,
                    "reservation_uuid": reservation['uuid'],
                    "section_id": reservation['section_id']
                }
            else:
                return {"success": False, "error": "No reservation updated"}

        except Exception as e:
            db.rollback()
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()

    def find_reservation_by_session(self, session_id: str) -> Dict[str, Any]:
        """Find reservation by session ID (using reservation_pin which contains session_id)"""
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # For now, we'll find by reservation_pin since it's created with session_id
            # In a real implementation, you might want to add a session_id column
            query = "SELECT * FROM storage_reservations WHERE reservation_pin = %s AND status IN (1, 2)"
            cursor.execute(query, (session_id,))
            return cursor.fetchone()
        finally:
            cursor.close()
            db.close()


